<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { useUserStore } from './src/stores/modules/user';
import { useAppStore } from './src/stores/modules/app';

const userStore = useUserStore();
const appStore = useAppStore();

onLaunch(() => {
  console.log('App Launch');

  // 初始化用户信息
  userStore.initProfile();

  // 初始化系统信息
  appStore.initSystemInfo();

  // 检查小程序更新
  checkForUpdate();
});

onShow(() => {
  console.log('App Show');
});

onHide(() => {
  console.log('App Hide');
});

/**
 * 检查小程序更新
 */
function checkForUpdate() {
  // #ifdef MP-WEIXIN
  const updateManager = uni.getUpdateManager();

  updateManager.onCheckForUpdate((res) => {
    console.log('检查更新结果:', res.hasUpdate);
  });

  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: (res) => {
        if (res.confirm) {
          updateManager.applyUpdate();
        }
      },
    });
  });

  updateManager.onUpdateFailed(() => {
    uni.showToast({
      title: '更新失败，请稍后重试',
      icon: 'none',
    });
  });
  // #endif
}
</script>

<style lang="scss">
@import './src/styles/global.scss';

/* 每个页面公共css */
</style>
