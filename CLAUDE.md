# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
始终用中文回复

## Developer Information

Primary developer: heimu

## Project Overview

This is a CDC (Centers for Disease Control) healthcare qualification examination WeChat Mini Program built with uni-app, Vue 3, TypeScript, and uview-plus UI library. The system provides a complete exam lifecycle from learning, practice, registration, to certification management for healthcare professionals.

## Development Commands

### Installation and Setup
```bash
npm install
```

### Development (use HBuilderX for best experience)
```bash
npm run dev
```
Or in HBuilderX: "运行" → "运行到小程序模拟器" → "微信开发者工具"

### Build for Production
```bash
npm run build:mp-weixin
```

### Code Quality Commands
```bash
npm run lint          # Check linting issues
npm run lint:fix      # Auto-fix linting issues  
npm run type-check    # TypeScript type checking
```

### Testing
No formal testing framework is configured. Manual testing files exist in `/tests/` for specific scenarios like login flows and WeChat integration.

### Platform-Specific Development
```bash
# Development with HBuilderX (recommended)
npm run dev

# Alternative development command
npm run dev:mp-weixin
```

**Important**: Use HBuilderX for development: "运行" → "运行到小程序模拟器" → "微信开发者工具"

## Architecture Overview

### Framework Stack
- **uni-app**: Cross-platform development framework targeting WeChat Mini Program
- **Vue 3**: Frontend framework using Composition API with `<script setup>`
- **TypeScript**: Strict typing enabled, avoid `any` types
- **Pinia**: State management with modular stores
- **uview-plus**: Comprehensive UI component library (uv-ui)
- **Vite**: Build tool with path aliases configured

### Directory Structure
- `pages/`: Main tabBar pages (login, info, study, exam, profile)
- `subpackages/`: Performance-optimized sub-packages for each major feature (info, study, exam, profile)
- `src/api/modules/`: RESTful API endpoints organized by domain (user, exam, study, etc.)
- `src/stores/modules/`: Pinia stores (user, app, exam)
- `src/components/business/`: Domain-specific reusable components
- `src/composables/`: Vue 3 composables for shared logic (camera auth, etc.)
- `src/types/`: TypeScript type definitions including full API response types
- `src/utils/`: Utility functions (request, storage, validation, formatting)

### Key Patterns

**Sub-package Architecture**: Code is split into sub-packages (info, study, exam, profile) for better loading performance and organized by feature domains.

**API Module Pattern**: APIs are organized in `src/api/modules/` by functionality:
- `user.ts`: Authentication, profile management
- `exam.ts`: Online/offline exams, results
- `study.ts`: Practice questions, progress tracking
- `certificate.ts`: Certificate management
- `feedback.ts`: User feedback system

**State Management**: Pinia stores follow domain separation:
- `user.ts`: User authentication state, profile data
- `app.ts`: Global app state, navigation
- `exam.ts`: Exam-specific state and progress

**Component Architecture**: Business components in `src/components/business/` are feature-specific (ExamCard, ExamHistory, FaceVerification, CameraPreview) while common components are reusable primitives.

### Configuration Files
- `pages.json`: uni-app routing, tabBar, and sub-package configuration
- `manifest.json`: WeChat Mini Program settings and permissions
- `vite.config.js`: Build configuration with path aliases (@, @/src, uview-plus)
- `uni.scss`: Global SCSS variables and uview-plus theme integration

## WeChat Mini Program Specifics

### Platform Conditionals
Use uni-app conditional compilation for platform-specific code:
```javascript
// #ifdef MP-WEIXIN
// WeChat Mini Program specific code
// #endif
```

### Navigation and Routing
- Main pages are defined in `pages.json` with tabBar configuration
- Sub-package pages follow the pattern: `subpackages/{domain}/pages/{page}/{page}.vue`
- Use `uni.navigateTo()`, `uni.redirectTo()`, `uni.switchTab()` for navigation

### WeChat Integration
- OAuth login via `uni.login()` and `uni.getUserProfile()`
- User status management: new → pending → approved/rejected
- Profile completion with image upload capabilities

## Code Standards

### TypeScript Usage
- Strict mode enabled in `tsconfig.json`
- Comprehensive type definitions in `src/types/api.d.ts`
- Component props must be typed
- API responses are fully typed

### Vue 3 Patterns
- Use Composition API with `<script setup lang="ts">`
- Reactive data with `ref()` and `reactive()`
- Computed properties and watchers as needed
- Component auto-import via easycom configuration

### Styling Approach
- SCSS preprocessing with global variables in `src/styles/variables.scss`
- uview-plus component library for UI consistency
- Responsive design considerations for different screen sizes
- Global styles in `src/styles/global.scss`

### API Integration
- HTTP client: luch-request configured in `src/utils/request.ts`
- Request/response interceptors for authentication and error handling
- API base URL configuration for different environments
- Consistent error handling and user feedback

## Important Development Notes

### Build Rules
From `.cursor/rules/build-rules.mdc`: Do not run build commands during development (不要运行编译命令). Always confirm requirements (95%+ certainty) before writing code.

### Face Recognition System
Recent development has focused on face verification and camera components:
- `FaceVerification.vue`: Main face verification component with four-corner masks
- `CameraPreview.vue`: Camera stream preview with state management
- `FaceGuide.vue`: User guidance for face positioning
- `CaptureButton.vue`: Photo capture functionality
- `useCameraAuth.ts`: Composable for camera permissions and error handling

The face verification system includes:
- Camera permission management with detailed error handling
- Four-corner mask overlay for face positioning guidance
- Real-time camera preview with state management
- Permission request flows and settings navigation
- Error handling for various camera failure scenarios

### WeChat Mini Program Permissions
Camera permissions are critical for face verification:
- Use `uni.getSetting()` to check current permissions
- Handle permission denial with guided settings navigation
- Implement proper error handling for camera initialization failures
- Support permission state changes during runtime

### Performance Considerations
- Sub-package preloading configured for optimal loading
- Component lazy loading where appropriate
- Image optimization for WeChat Mini Program size limits

### User Flow Architecture
The app manages complex user verification flows:
1. WeChat OAuth → Basic user creation
2. Profile completion → Pending status
3. Institution review → Approved/Rejected status
4. Feature access based on approval status

### Exam System Architecture
Supports both online and offline examination modes:
- Online: Real-time proctoring, anti-cheating measures
- Offline: Registration and venue management
- Results tracking and certificate generation

This architecture emphasizes modular design, type safety, performance optimization, and WeChat Mini Program best practices while maintaining a clean separation of concerns across the examination platform's complex feature set.

## Technical Constraints (强制执行)

### Mandatory Technology Stack
- **Development Tool**: HBuilderX (≥ 3.8.0) - MUST be used for compilation and building
- **UI Library Priority**: 
  - **Primary**: uview-plus (3.4.43) with uv-ui prefix - Vue 3 compatible version
  - **Secondary**: Native uni-app components when uview-plus doesn't meet requirements
- **HTTP Client**: luch-request (≥ 3.1.0) - uniapp ecosystem HTTP library
- **State Management**: Pinia Setup Store pattern - MANDATORY for all stores
- **Component Syntax**: `<script setup lang="ts">` - MANDATORY for all components

### Development Environment Rules
- **MUST use HBuilderX** for project compilation and building
- **PROHIBITED**: Using other build tools (Vue CLI, Vite CLI, etc.)
- All compilation should go through HBuilderX built-in compiler

### Code Quality Enforcement
- **TypeScript**: Strict mode enabled - `any` type is PROHIBITED
- **ESLint + Prettier**: Code MUST pass both linting and formatting checks
- **File Length Limit**: Maximum 500 lines per file, 50 lines per function
- **Component Structure**: MUST follow the template pattern with typed props and emits

### Performance Constraints
- **Package Size**: Main package ≤ 2MB, each subpackage ≤ 2MB
- **Resource Optimization**: 
  - Images: Prefer CDN, use webp format, compress local images
  - Lazy Loading: MANDATORY for non-first-screen content
  - Virtual Lists: MANDATORY for lists with 100+ items
- **Component Import**: MUST use easycom auto-import, manual imports PROHIBITED

### WeChat Mini Program Specific Constraints
- **API Restrictions**: PROHIBITED use of Web-Only APIs (window, document, localStorage)
- **Storage**: Use uni.setStorageSync (10MB limit awareness required)
- **Navigation**: Mind 10-layer page stack limit, use uni.reLaunch/redirectTo appropriately
- **Update Management**: MUST implement wx.getUpdateManager in App.vue onLaunch
- **User Authorization**: MUST follow WeChat privacy guidelines for sensitive data

## Development Workflow

### Code Standards
- Strict TypeScript mode enabled - avoid `any` types
- Use Composition API with `<script setup lang="ts">`
- Follow ESLint + Prettier configuration
- Naming conventions: PascalCase for components, camelCase for variables and functions
- Git commit messages follow Conventional Commits format

### Component Development
- Use uview-plus (uv-ui) components for UI consistency
- Business components go in `src/components/business/`
- Composables for shared logic go in `src/composables/`
- Component auto-import via easycom configuration in `pages.json`
