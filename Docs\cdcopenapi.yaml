openapi: 3.0.3
info:
  title: 疾控医护任职资格考试系统 API
  description: |
    由认知API架构师根据产品需求文档（2025年6月14日版）生成的API技术规范。
    该系统旨在为疾控机构医护人员提供一站式任职资格考试及学习辅助服务。
  version: 1.5.0
servers:
  - url: https://api.yourdomain.com/api/v1
    description: 生产环境服务器
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器

tags:
  - name: 认证与用户 (Auth & User)
    description: 涉及用户登录、注册、状态管理和资料提交。
  - name: 信息中心 (Information)
    description: 公告、政策、通知等资讯的获取。
  - name: 学习中心 (Learning)
    description: 题库练习相关功能。
  - name: 考试中心 (Exam)
    description: 线上考试、线下报名及历史记录。
  - name: 个人中心 (Profile)
    description: 个人信息、证书管理等。
  - name: 通用 (Common)
    description: 反馈、关于我们等通用功能。

paths:
  # 认证与用户
  /auth/wechat-login:
    post:
      tags:
        - 认证与用户 (Auth & User)
      summary: 微信授权登录
      description: |
        用户通过微信授权后，使用code换取系统token和用户的核心会话状态。
        这个响应包含了驱动应用全局逻辑所需的最少信息，如用户状态和VIP权限。
        完整的个人详细资料请通过 /profile/me 接口按需获取。
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: 通过 wx.login() 获取的临时登录凭证。
                  example: "0a3j3J1c0c..."
              required:
                - code
      responses:
        '200':
          description: 登录成功。返回Token和用户核心会话状态。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        description: "用户核心会话状态对象"
                        properties:
                          token:
                            type: string
                            description: 用于后续请求的JWT。
                          userStatus:
                            $ref: '#/components/schemas/UserStatus'
                          isVip:
                            type: boolean
                            description: 是否为VIP用户。此信息用于控制对学习中心等功能的访问。
                          vipExpiryDate:
                            type: string
                            format: date
                            description: VIP到期日期。非VIP或用户状态不为'approved'时，此字段可为空。
                        required:
                          - token
                          - userStatus
                          - isVip
              examples:
                approvedUser:
                  summary: "已审核通过的用户"
                  value:
                    code: 200
                    message: "登录成功"
                    data:
                      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhMWIyYzNkNC1lNWY2LTc4OTAtMTIzNC01Njc4OTBhYmNkZWYiLCJpYXQiOjE2ODU4ODg4MDB9.somesampletoken"
                      userStatus: "approved"
                      isVip: true
                      vipExpiryDate: "2026-12-31"
                newUser:
                  summary: "新用户"
                  value:
                    code: 200
                    message: "登录成功，请完善个人资料"
                    data:
                      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJuZXdfdXNlcl9pZCIsImlhdCI6MTY4NTg4ODgwMH0.anothersampletoken"
                      userStatus: "new"
                      isVip: false
                      vipExpiryDate: null
                rejectedUser:
                  summary: "被拒绝的用户"
                  value:
                    code: 200
                    message: "登录成功，您的资料审核未通过"
                    data:
                      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJyZWplY3RlZF91c2VyX2lkIiwiaWF0IjoxNjg1ODg4ODgwMH0.yetanothertoken"
                      userStatus: "rejected"
                      isVip: false
                      vipExpiryDate: null
        '400':
          $ref: '#/components/responses/BadRequest'

  /institutions:
    get:
      tags:
        - 认证与用户 (Auth & User)
      summary: 获取机构列表
      description: 获取所有可选的机构列表，这是一个公开接口，无需认证。
      responses:
        '200':
          description: 成功获取机构列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Institution'
              examples:
                default:
                  summary: "成功获取机构列表"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - id: "11111111-1111-1111-1111-111111111111"
                        name: "市疾控中心"
                      - id: "22222222-2222-2222-2222-222222222222"
                        name: "区疾控中心"

  /positions:
    get:
      tags:
        - 认证与用户 (Auth & User)
      summary: 获取职位列表
      description: 获取所有可选的职位列表，这是一个公开接口，无需认证。
      responses:
        '200':
          description: 成功获取职位列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Position'
              examples:
                default:
                  summary: "成功获取职位列表"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - id: "33333333-3333-3333-3333-333333333333"
                        name: "预防接种医生"
                      - id: "44444444-4444-4444-4444-444444444444"
                        name: "检验技师"

  /profile:
    post:
      tags:
        - 认证与用户 (Auth & User)
      summary: 提交个人资料 (新用户注册)
      description: 新用户或未提交资料的用户提交个人从业信息以供审核。照片需要通过文件上传。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileSubmissionRequest'
      responses:
        '201':
          description: 资料提交成功，等待审核。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                default:
                  summary: "提交成功"
                  value:
                    code: 201
                    message: "资料提交成功，等待审核"
                    data: null
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
    put:
      tags:
        - 认证与用户 (Auth & User)
      summary: 修改个人资料 (审核不通过后)
      description: 当用户资料审核被驳回后，可调用此接口修改并重新提交。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileSubmissionRequest'
      responses:
        '200':
          description: 资料修改并重新提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                default:
                  summary: "修改成功"
                  value:
                    code: 200
                    message: "资料修改成功，等待审核"
                    data: null
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /profile/me:
    get:
      tags:
        - 个人中心 (Profile)
      summary: 获取当前用户信息
      description: 获取当前登录用户的详细个人信息，通常在用户进入“个人中心”页面时调用。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取用户信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
              examples:
                approvedUser:
                  summary: "审核通过的用户示例"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      id: "a1b2c3d4-e5f6-7890-1234-567890abcdef"
                      wechatNickname: "奋斗的企鹅"
                      wechatAvatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTK4Q..."
                      status: "approved"
                      name: "张三"
                      phone: "138****8000"
                      idCardNumber: "4401**********0001"
                      photoUrl: "https://cdn.yourdomain.com/photos/a1b2c3d4.jpg"
                      institutionName: "市疾控中心"
                      positionName: "预防接种医生"
                      certificateExpiryDate: "2026-12-31"
                      isVip: true
                      vipExpiryDate: "2026-12-31"
                      rejectionReason: null
                      createdAt: "2024-05-20T10:00:00Z"
                rejectedUser:
                  summary: "审核被拒的用户示例"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      id: "b2c3d4e5-f6a7-8901-2345-67890abcdef1"
                      wechatNickname: "迷茫的考拉"
                      wechatAvatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/another_url..."
                      status: "rejected"
                      name: "李四"
                      phone: "139****9000"
                      idCardNumber: "4402**********0002"
                      photoUrl: "https://cdn.yourdomain.com/photos/b2c3d4e5.jpg"
                      institutionName: "区疾控中心"
                      positionName: "检验技师"
                      certificateExpiryDate: null
                      isVip: false
                      vipExpiryDate: null
                      rejectionReason: "您上传的本人照片模糊，无法识别人脸，请上传高清正面照。"
                      createdAt: "2024-06-10T11:00:00Z"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # 信息中心
  /articles:
    get:
      tags:
        - 信息中心 (Information)
      summary: 获取资讯列表
      description: 获取公告、政策法规、重要通知的列表。通过type参数进行筛选。
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ArticleType'
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: 成功获取资讯列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/Article'
                          total:
                            type: integer
                          page:
                            type: integer
                          pageSize:
                            type: integer
              examples:
                default:
                  summary: "成功获取资讯分页列表"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      items:
                        - id: "article-uuid-001"
                          title: "关于2025年上半年任职资格考试的通知"
                          type: "notice"
                          isPinned: true
                          createdAt: "2024-06-01T09:00:00Z"
                        - id: "article-uuid-002"
                          title: "新版《疫苗管理法》解读"
                          type: "policy"
                          isPinned: false
                          createdAt: "2024-05-28T14:30:00Z"
                      total: 2
                      page: 1
                      pageSize: 10
        '401':
          $ref: '#/components/responses/Unauthorized'

  /articles/{articleId}:
    get:
      tags:
        - 信息中心 (Information)
      summary: 获取资讯详情
      description: 根据ID获取单条资讯的详细内容。
      security:
        - bearerAuth: []
      parameters:
        - name: articleId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取资讯详情。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Article'
              examples:
                default:
                  summary: "成功获取单条资讯详情"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      id: "article-uuid-001"
                      title: "关于2025年上半年任职资格考试的通知"
                      content: "<h1>重要通知</h1><p>请各位医护人员注意，2025年上半年任职资格考试将于6月20日举行...</p>"
                      type: "notice"
                      isPinned: true
                      createdAt: "2024-06-01T09:00:00Z"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  # 学习中心
  /learning/question-bank/categories:
    get:
      tags:
        - 学习中心 (Learning)
      summary: 获取题库分类列表
      description: 获取所有可供练习的题库分类。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取分类列表。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/QuestionBankCategory'
              examples:
                default:
                  summary: "成功获取题库分类"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - id: "cat-uuid-01"
                        name: "免疫规划基础"
                        totalQuestions: 150
                      - id: "cat-uuid-02"
                        name: "传染病防治"
                        totalQuestions: 200
        '401':
          $ref: '#/components/responses/Unauthorized'

  /learning/question-bank/practice-questions:
    get:
      tags:
        - 学习中心 (Learning)
      summary: 获取一组练习题
      description: 根据分类ID随机获取一组（例如10道）练习题。
      security:
        - bearerAuth: []
      parameters:
        - name: categoryId
          in: query
          required: true
          schema:
            type: string
            format: uuid
        - name: count
          in: query
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: 成功获取练习题。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/QuestionWithSolution'
              examples:
                default:
                  summary: "成功获取一组练习题"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - id: "q-uuid-001"
                        type: "single_choice"
                        stem: "以下哪项是病毒性肝炎的主要传播途径？"
                        options:
                          - key: "A"
                            value: "呼吸道传播"
                          - key: "B"
                            value: "消化道传播"
                          - key: "C"
                            value: "接触传播"
                        correctAnswer: ["B"]
                        explanation: "甲型和戊型肝炎主要通过消化道传播。"
                      - id: "q-uuid-002"
                        type: "judgment"
                        stem: "所有疫苗都应在2-8℃条件下储存和运输。"
                        options: []
                        correctAnswer: ["false"]
                        explanation: "部分疫苗如口服脊髓灰质炎减毒活疫苗（OPV）需要在-20℃下储存。"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 练习次数已用完。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ErrorResponse'

  # 考试中心
  /exams/current:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取本期待考列表
      description: 获取当前用户需要参加的线上或线下考试列表。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取待考列表。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Exam'
              examples:
                default:
                  summary: "成功获取待考列表（含各种状态）"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - id: "exam-online-01"
                        name: "2025年上半年线上资格考试"
                        type: "online"
                        status: "ready_to_start"
                        startTime: "2025-06-20T09:00:00Z"
                        endTime: "2025-06-30T17:00:00Z"
                        canRetry: true
                        attemptResult: null
                        bookingDetails: null
                      - id: "exam-offline-01"
                        name: "2025年操作技能线下考核"
                        type: "offline"
                        status: "booking_available"
                        startTime: "2025-07-10T00:00:00Z"
                        endTime: "2025-07-12T23:59:59Z"
                        canRetry: false
                        attemptResult: null
                        bookingDetails: null
                      - id: "exam-offline-02"
                        name: "2025年高级技能认证（线下）"
                        type: "offline"
                        status: "booked"
                        startTime: "2025-08-01T00:00:00Z"
                        endTime: "2025-08-02T23:59:59Z"
                        canRetry: false
                        attemptResult: null
                        bookingDetails:
                          bookingId: "booking-uuid-xyz-01"
                          venueName: "市疾控中心一号考场"
                          scheduleStartTime: "2025-08-01T09:00:00Z"
                      - id: "exam-online-02"
                        name: "2025年第二季度专项测试"
                        type: "online"
                        status: "passed"
                        startTime: "2025-05-01T09:00:00Z"
                        endTime: "2025-05-10T17:00:00Z"
                        canRetry: false
                        attemptResult:
                          score: 95.0
                          completedAt: "2025-05-02T10:30:00Z"
                        bookingDetails: null
                      - id: "exam-online-03"
                        name: "2025年第一季度补考"
                        type: "online"
                        status: "failed"
                        startTime: "2025-04-15T09:00:00Z"
                        endTime: "2025-04-20T17:00:00Z"
                        canRetry: true
                        attemptResult:
                          score: 58.5
                          completedAt: "2025-04-16T11:00:00Z"
                        bookingDetails: null
                      - id: "exam-online-04"
                        name: "2024年终极考核"
                        type: "online"
                        status: "failed_final"
                        startTime: "2024-12-10T09:00:00Z"
                        endTime: "2024-12-15T17:00:00Z"
                        canRetry: false
                        attemptResult:
                          score: 45.0
                          completedAt: "2024-12-11T14:20:00Z"
                        bookingDetails: null
                      - id: "exam-online-05"
                        name: "问答题专项阅卷考试"
                        type: "online"
                        status: "pending_grading"
                        startTime: "2025-06-01T09:00:00Z"
                        endTime: "2025-06-05T17:00:00Z"
                        canRetry: false
                        attemptResult:
                          score: null
                          completedAt: "2025-06-02T15:30:00Z"
                        bookingDetails: null
                      - id: "exam-online-06"
                        name: "已结束的线上考试"
                        type: "online"
                        status: "finished"
                        startTime: "2024-03-01T09:00:00Z"
                        endTime: "2024-03-05T17:00:00Z"
                        canRetry: false
                        attemptResult: null
                        bookingDetails: null
        '401':
          $ref: '#/components/responses/Unauthorized'
  
  /exams/history:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取历史考试记录
      description: 获取用户已完成的所有考试记录，支持分页。
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: 成功获取历史记录列表。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ExamAttempt'
                          total:
                            type: integer
                          page:
                            type: integer
                          pageSize:
                            type: integer
              examples:
                default:
                  summary: "成功获取历史考试记录"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      items:
                        - id: "attempt-uuid-hist-01"
                          examName: "2024年下半年线上资格考试"
                          examType: "online"
                          completedAt: "2024-12-15T10:30:00Z"
                          score: 92.5
                          status: "passed"
                      total: 1
                      page: 1
                      pageSize: 10
        '401':
          $ref: '#/components/responses/Unauthorized'
          
  /exams/online/{examId}/rules:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取线上考试考前须知
      description: 在正式开始考试前，获取考前阅读的规则和须知。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取考前须知。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          rules:
                            type: string
                            description: 考前须知内容，支持富文本。
              examples:
                default:
                  summary: "成功获取考前须知"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      rules: "<h2>考前须知</h2><ol><li>本次考试包含人脸识别，请确保环境光线充足。</li><li>考试期间请勿切出小程序，否则将被记录。</li></ol>"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /exams/online/{examId}/attempts:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 开始线上考试尝试 (含人脸识别)
      description: |
        这是一个关键的原子操作。用户调用此接口开始一次新的考试尝试。
        请求体中必须包含用于人脸识别的现场照片。
        后端完成人脸比对，成功后才返回唯一的`attemptId`和本次考试的题目。
        失败则返回错误，不允许进入答题。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                faceImage:
                  type: string
                  format: binary
                  description: 用户在考前通过摄像头现场拍摄的照片。
              required:
                - faceImage
      responses:
        '201':
          description: 人脸识别成功，考试开始。返回考题和attemptId。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          attemptId:
                            type: string
                            format: uuid
                          questions:
                            type: array
                            items:
                              $ref: '#/components/schemas/QuestionForDisplay'
              examples:
                default:
                  summary: "考试开始成功"
                  value:
                    code: 201
                    message: "考试开始"
                    data:
                      attemptId: "attempt-uuid-full-exam-12345"
                      questions:
                        - id: "q-exam-sc-01"
                          type: "single_choice"
                          stem: "卡介苗（BCG）属于哪一类疫苗？"
                          options:
                            - key: "A"
                              value: "灭活疫苗"
                            - key: "B"
                              value: "减毒活疫苗"
                            - key: "C"
                              value: "亚单位疫苗"
                            - key: "D"
                              value: "类毒素疫苗"
                        - id: "q-exam-sc-02"
                          type: "single_choice"
                          stem: "根据国家规定，大部分疫苗在储存和运输过程中应保持在什么温度范围内？"
                          options:
                            - key: "A"
                              value: "-20℃ 至 -15℃"
                            - key: "B"
                              value: "0℃ 至 4℃"
                            - key: "C"
                              value: "2℃ 至 8℃"
                            - key: "D"
                              value: "8℃ 至 15℃"
                        - id: "q-exam-sc-03"
                          type: "single_choice"
                          stem: "下列哪项是《中华人民共和国传染病防治法》规定的甲类传染病？"
                          options:
                            - key: "A"
                              value: "艾滋病"
                            - key: "B"
                              value: "狂犬病"
                            - key: "C"
                              value: "鼠疫"
                            - key: "D"
                              value: "流行性感冒"
                        - id: "q-exam-sc-04"
                          type: "single_choice"
                          stem: "医护人员发生职业暴露后，进行首次报告和检测的最重要时间点是？"
                          options:
                            - key: "A"
                              value: "暴露后24小时内"
                            - key: "B"
                              value: "暴露后立即"
                            - key: "C"
                              value: "暴露后一周内"
                            - key: "D"
                              value: "暴露后一个月内"
                        - id: "q-exam-sc-05"
                          type: "single_choice"
                          stem: "处理医疗废物时，装有病原体培养基、标本和菌种、毒种保存液的废物应放入哪种颜色的包装袋？"
                          options:
                            - key: "A"
                              value: "黑色"
                            - key: "B"
                              value: "红色"
                            - key: "C"
                              value: "黄色"
                            - key: "D"
                              value: "蓝色"
                        - id: "q-exam-sc-06"
                          type: "single_choice"
                          stem: "对于疑似狂犬病暴露III级伤口，除了彻底清洗和消毒外，首要的紧急措施是？"
                          options:
                            - key: "A"
                              value: "立即注射狂犬病疫苗"
                            - key: "B"
                              value: "立即缝合伤口"
                            - key: "C"
                              value: "在伤口周围浸润注射狂犬病被动免疫制剂"
                            - key: "D"
                              value: "口服抗生素"
                        - id: "q-exam-mc-01"
                          type: "multiple_choice"
                          stem: "以下哪些属于标准预防（Standard Precautions）的措施？（多选）"
                          options:
                            - key: "A"
                              value: "手卫生"
                            - key: "B"
                              value: "根据风险评估使用个人防护装备（如手套、口罩）"
                            - key: "C"
                              value: "安全注射"
                            - key: "D"
                              value: "将所有患者都视为具有潜在的传染性"
                        - id: "q-exam-mc-02"
                          type: "multiple_choice"
                          stem: "下列疫苗中，哪些属于减毒活疫苗？（多选）"
                          options:
                            - key: "A"
                              value: "麻腮风联合疫苗（MMR）"
                            - key: "B"
                              value: "脊髓灰质炎灭活疫苗（IPV）"
                            - key: "C"
                              value: "口服轮状病毒活疫苗"
                            - key: "D"
                              value: "吸附无细胞百白破联合疫苗（DTaP）"
                        - id: "q-exam-mc-03"
                          type: "multiple_choice"
                          stem: "在处理疑似食源性疾病暴发时，疾控人员需要采取的措施包括？（多选）"
                          options:
                            - key: "A"
                              value: "开展流行病学调查"
                            - key: "B"
                              value: "采集患者生物样本和可疑食品样本"
                            - key: "C"
                              value: "开展卫生学调查"
                            - key: "D"
                              value: "进行健康教育"
                        - id: "q-exam-mc-04"
                          type: "multiple_choice"
                          stem: "接种疫苗后可能出现的一般反应（局部反应）包括？（多选）"
                          options:
                            - key: "A"
                              value: "接种部位红肿"
                            - key: "B"
                              value: "接种部位硬结"
                            - key: "C"
                              value: "接种部位疼痛"
                            - key: "D"
                              value: "过敏性休克"
                        - id: "q-exam-mc-05"
                          type: "multiple_choice"
                          stem: "进行终末消毒时，需要考虑的因素有哪些？（多选）"
                          options:
                            - key: "A"
                              value: "病原体的种类和抵抗力"
                            - key: "B"
                              value: "消毒对象的材质和特性"
                            - key: "C"
                              value: "消毒剂的性质和浓度"
                            - key: "D"
                              value: "环境的温度和湿度"
                        - id: "q-exam-mc-06"
                          type: "multiple_choice"
                          stem: "根据《突发公共卫生事件应急条例》，以下哪些情况属于突发公共卫生事件？（多选）"
                          options:
                            - key: "A"
                              value: "发生传染病暴发、流行"
                            - key: "B"
                              value: "出现不明原因的群体性疾病"
                            - key: "C"
                              value: "发生重大食物和职业中毒事件"
                            - key: "D"
                              value: "一家医院发生一起院内感染"
                        - id: "q-exam-jd-01"
                          type: "judgment"
                          stem: "使用抗生素可以有效治疗由病毒引起的普通感冒。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-jd-02"
                          type: "judgment"
                          stem: "所有儿童都必须严格按照国家免疫规划程序规定的时间间隔进行疫苗接种，不能提前或推迟。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-jd-03"
                          type: "judgment"
                          stem: "在进行手卫生时，使用含酒精的免洗手消毒液的效果等同于使用肥皂和流动水洗手。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-jd-04"
                          type: "judgment"
                          stem: "接触了肺结核病人就一定会被感染。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-jd-05"
                          type: "judgment"
                          stem: "接种疫苗后出现的任何不良事件都属于疫苗接种异常反应。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-jd-06"
                          type: "judgment"
                          stem: "在医疗机构中，锐器伤是导致血源性疾病职业暴露最常见的原因。"
                          options:
                            - key: true
                              value: "正确"
                            - key: false
                              value: "错误"
                        - id: "q-exam-es-01"
                          type: "essay"
                          stem: "请简述实施手卫生的“五大时刻”（Five Moments for Hand Hygiene）具体指哪些时刻？"
                          options: []
                        - id: "q-exam-es-02"
                          type: "essay"
                          stem: "当您在接种门诊遇到一位对接种疫苗犹豫不决的儿童家长时，您将如何进行有效的沟通？请列出至少三个沟通要点。"
                          options: []
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 人脸识别失败或已无重试机会。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ErrorResponse'
        '404':
          $ref: '#/components/responses/NotFound'

  /exams/online/attempts/{attemptId}/answers:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 提交线上考试答案 (最终交卷)
      description: 提交指定考试尝试的所有答案，并结束考试。
      security:
        - bearerAuth: []
      parameters:
        - name: attemptId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnswerSubmission'
            example:
              answers:
                - questionId: "q-exam-uuid-001"
                  answer: ["A", "B", "D"]
                - questionId: "q-exam-uuid-002"
                  answer: ["true"]
      responses:
        '200':
          description: 答卷提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                default:
                  summary: "交卷成功"
                  value:
                    code: 200
                    message: "答卷提交成功！"
                    data: null
        '401':
          $ref: '#/components/responses/Unauthorized'
    patch:
      tags:
        - 考试中心 (Exam)
      summary: 暂存线上考试答案 (保障健壮性)
      description: |
        在考试过程中，客户端可以定时或在用户回答一题后调用此接口，将答案增量保存到服务器。
        这能有效防止因小程序闪退、网络中断等意外情况导致答题数据丢失。
      security:
        - bearerAuth: []
      parameters:
        - name: attemptId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnswerSubmission'
            example:
              answers:
                - questionId: "q-exam-uuid-003"
                  answer: ["C"]
      responses:
        '204':
          description: 答案暂存成功。
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'

  /exams/online/attempts/{attemptId}/anticheat-logs:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 上报防作弊日志 (支持批量和图片)
      description: |
        考试过程中，前端监听到切屏、人脸抓拍等行为时，调用此接口上报。
        支持将多个事件打包在一个请求中。
        请求类型为 multipart/form-data。
        - 'logs' 部分: 一个JSON字符串，内容为事件对象数组。
        - 文件部分: 对于类型为 'face_capture' 的事件，其 eventData.fileKey 必须与一个文件部分的 name 对应。
      security:
        - bearerAuth: []
      parameters:
        - name: attemptId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logs:
                  type: string
                  description: "事件数组的JSON字符串。例如: '[{\"eventType\": \"screen_switch\"}, {\"eventType\": \"face_capture\", \"eventData\": {\"fileKey\": \"capture1\"}}]'"
              required:
                - logs
            encoding:
              logs:
                contentType: application/json
      responses:
        '204':
          description: 日志记录成功。
        '401':
          $ref: '#/components/responses/Unauthorized'

  /exams/offline/{examId}/schedules:
    get:
      tags:
        - 考试中心 (Exam)
      summary: 获取线下考试的考场和场次
      description: 查看指定线下考试的所有可用考场及各考场下的时间安排和名额。
      security:
        - bearerAuth: []
      parameters:
        - name: examId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 成功获取考场和场次信息。
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/ApiResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/OfflineVenueSchedule'
              examples:
                default:
                  summary: "成功获取考场与场次"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      - venueId: "venue-uuid-01"
                        venueName: "市疾控中心一号考场"
                        schedules:
                          - scheduleId: "schedule-uuid-01-a"
                            startTime: "2025-07-10T09:00:00Z"
                            endTime: "2025-07-10T11:00:00Z"
                            totalSlots: 50
                            availableSlots: 12
                            venueAddress: "市疾控中心大楼3楼301室"
                            contactPerson: "李老师"
                            contactPhone: "0755-12345678"
                            notes: "请提前30分钟到场，携带身份证件"
                          - scheduleId: "schedule-uuid-01-b"
                            startTime: "2025-07-10T14:00:00Z"
                            endTime: "2025-07-10T16:00:00Z"
                            totalSlots: 50
                            availableSlots: 0
                            venueAddress: "市疾控中心大楼3楼301室"
                            contactPerson: "李老师"
                            contactPhone: "0755-12345678"
                            notes: "请提前30分钟到场，携带身份证件"
        '401':
          $ref: '#/components/responses/Unauthorized'

  /exams/offline/bookings:
    post:
      tags:
        - 考试中心 (Exam)
      summary: 报名线下考试场次
      description: 为当前用户预约一个特定的线下考试场次。
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                scheduleId:
                  type: string
                  format: uuid
                  description: 考场时间安排的唯一ID。
              required:
                - scheduleId
      responses:
        '201':
          description: 报名成功。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OfflineBooking'
              examples:
                default:
                  summary: "报名成功"
                  value:
                    code: 201
                    message: "报名成功"
                    data:
                      bookingId: "booking-uuid-xyz"
                      examName: "2025年操作技能线下考核"
                      venueName: "市疾控中心一号考场"
                      startTime: "2025-07-10T09:00:00Z"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 报名失败（如名额已满或不符合条件）。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /exams/offline/bookings/{bookingId}:
    delete:
      tags:
        - 考试中心 (Exam)
      summary: 取消线下考试报名
      description: 取消一个已有的线下考试预约。
      security:
        - bearerAuth: []
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: 成功取消报名。
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: 取消失败（如已过取消时限）。

  # 个人中心
  /profile/me/certificates:
    get:
      tags:
        - 个人中心 (Profile)
      summary: 获取我的证书
      description: 获取当前用户的所有证书信息，包括当前有效、审批中和历史证书。
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功获取证书信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          currentCertificate:
                            $ref: '#/components/schemas/Certificate'
                          pendingCertificate:
                            $ref: '#/components/schemas/Certificate'
                          historyCertificates:
                            type: array
                            items:
                              $ref: '#/components/schemas/Certificate'
              examples:
                default:
                  summary: "成功获取证书信息"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      currentCertificate:
                        id: "cert-uuid-current"
                        name: "接种门诊上岗资格证"
                        status: "valid"
                        imageUrl: "https://cdn.yourdomain.com/certs/cert-current.png"
                        issueDate: "2023-12-31"
                        expiryDate: "2026-12-31"
                      pendingCertificate: null
                      historyCertificates:
                        - id: "cert-uuid-expired"
                          name: "接种门诊上岗资格证"
                          status: "expired"
                          imageUrl: "https://cdn.yourdomain.com/certs/cert-expired.png"
                          issueDate: "2020-12-31"
                          expiryDate: "2023-12-30"
        '401':
          $ref: '#/components/responses/Unauthorized'

  # 通用
  /feedback:
    post:
      tags:
        - 通用 (Common)
      summary: 提交投诉与建议
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: string
                  description: 反馈内容。
              required:
                - content
      responses:
        '201':
          description: 提交成功。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                default:
                  summary: "反馈提交成功"
                  value:
                    code: 201
                    message: "感谢您的反馈！"
                    data: null
        '401':
          $ref: '#/components/responses/Unauthorized'

  /app/info:
    get:
      tags:
        - 通用 (Common)
      summary: 获取关于我们的信息
      responses:
        '200':
          description: 成功获取应用信息。
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          appName:
                            type: string
                          version:
                            type: string
                          description:
                            type: string
                          copyright:
                            type: string
              examples:
                default:
                  summary: "成功获取应用信息"
                  value:
                    code: 200
                    message: "OK"
                    data:
                      appName: "疾控医护任职资格考试系统"
                      version: "1.5.0"
                      description: "为疾控机构医护人员提供一站式任职资格考试及学习辅助服务。"
                      copyright: "Copyright © 2025 Your Company. All Rights Reserved."

components:
  schemas:
    # --- Generic API Response Wrapper ---
    ApiResponse:
      type: object
      description: 统一API响应包裹结构
      properties:
        code:
          type: integer
          description: 业务状态码，200表示成功
          example: 200
        message:
          type: string
          description: 响应消息
          example: "OK"
        data:
          description: 响应数据，可以是任意类型
      required:
        - code
        - message

    # --- Enums & Basic Types ---
    UserStatus:
      type: string
      enum: [new, pending_review, approved, rejected]
      description: |
        用户状态:
        - `new`: 新微信用户，未提交资料
        - `pending_review`: 已提交资料，待审核
        - `approved`: 审核通过，正式用户
        - `rejected`: 审核不通过
    ArticleType:
      type: string
      enum: [announcement, policy, notice]
      description: |
        资讯类型:
        - `announcement`: 公告
        - `policy`: 政策法规
        - `notice`: 重要通知
    ExamType:
      type: string
      enum: [online, offline]
    ExamUserStatus:
      type: string
      description: |
        用户视角下的考试状态，用于驱动前端UI展示和操作。
        - `not_started`: 考试尚未开始（在`startTime`之前）。
        - `booking_available`: (线下) 可报名。
        - `booked`: (线下) 已报名，等待考试开始。
        - `ready_to_start`: (线上) 可开始考试（在时间窗口内，且用户未参与）。
        - `in_progress`: (线上) 考试已开始，但未提交。
        - `passed`: 已通过。
        - `failed`: 未通过，但仍可重考 (`canRetry`为`true`)。
        - `failed_final`: 未通过，且已无重考机会。
        - `finished`: 考试已正常结束（过了`endTime`）。
        - `pending_grading`: (线上) 已提交，等待人工阅卷。
      enum:
        - not_started
        - booking_available
        - booked
        - ready_to_start
        - in_progress
        - passed
        - failed
        - failed_final
        - finished
        - pending_grading
    QuestionType:
      type: string
      enum: [single_choice, multiple_choice, judgment, essay]

    # --- Request Bodies ---
    ProfileSubmissionRequest:
      type: object
      properties:
        name:
          type: string
          description: 真实姓名
          example: 张三
        phone:
          type: string
          description: 手机号码
          example: '13800138000'
        idCardNumber:
          type: string
          description: 身份证号码
          example: '******************'
        photo:
          type: string
          format: binary
          description: |
            本人近期正面免冠证件照或清晰的生活照。
            建议前端在上传前校验文件大小，例如限制在200KB以内。
        institutionId:
          type: string
          format: uuid
          description: 隶属机构ID
        positionId:
          type: string
          format: uuid
          description: 职位ID
      required:
        - name
        - phone
        - idCardNumber
        - photo
        - institutionId
        - positionId
    
    AnswerSubmission:
      type: object
      properties:
        answers:
          type: array
          items:
            type: object
            properties:
              questionId:
                type: string
                format: uuid
              answer:
                type: array
                items:
                  type: string
                description: "用户的答案key集合。单选题数组只有一项，如['B']；多选题有多项，如['A', 'D']；判断题如['true']"
            required:
              - questionId
              - answer
      required:
        - answers

    # --- Main Resources ---
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        wechatNickname:
          type: string
        wechatAvatarUrl:
          type: string
          format: uri
        status:
          $ref: '#/components/schemas/UserStatus'
        name:
          type: string
          description: 真实姓名 (审核通过后返回)
        phone:
          type: string
          description: 手机号 (后端返回时应脱敏，如 '138****8000')
        idCardNumber:
          type: string
          description: 身份证号 (后端返回时应脱敏，如 '4401**********0001')
        photoUrl:
          type: string
          format: uri
          description: 注册时提交的照片URL
        institutionName:
          type: string
          description: 隶属机构名称
        positionName:
          type: string
          description: 职位名称
        certificateExpiryDate:
          type: string
          format: date
          description: 当前有效证书的到期日
        isVip:
          type: boolean
          description: 是否为VIP用户
        vipExpiryDate:
          type: string
          format: date
          description: VIP到期日期，非VIP用户此字段可为空
        rejectionReason:
          type: string
          nullable: true
          description: "审核拒绝原因。当 status 为 'rejected' 时返回，说明审核不通过的具体原因。"
        createdAt:
          type: string
          format: date-time

    Institution:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 机构唯一标识
        name:
          type: string
          description: 机构名称
      required:
        - id
        - name

    Position:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 职位唯一标识
        name:
          type: string
          description: 职位名称
      required:
        - id
        - name

    Article:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        content:
          type: string
          description: 资讯正文，支持富文本HTML
        type:
          $ref: '#/components/schemas/ArticleType'
        isPinned:
          type: boolean
          description: 是否置顶
        createdAt:
          type: string
          format: date-time
    
    QuestionBankCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        totalQuestions:
          type: integer
    
    QuestionForDisplay:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          $ref: '#/components/schemas/QuestionType'
        stem:
          type: string
          description: 题干
        options:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                example: A
              value:
                type: string
                example: '选项内容'

    QuestionWithSolution:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          $ref: '#/components/schemas/QuestionType'
        stem:
          type: string
          description: 题干
        options:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                example: A
              value:
                type: string
                example: '选项内容'
        correctAnswer:
          type: array
          items:
            type: string
          description: 正确答案的key集合。
          example: ['A', 'C']
        explanation:
          type: string
          description: 答案解析。

    Exam:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        type:
          $ref: '#/components/schemas/ExamType'
        status:
          $ref: '#/components/schemas/ExamUserStatus'
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        canRetry:
          type: boolean
          description: 是否允许重考
        attemptResult:
          type: object
          nullable: true
          description: "当考试已提交时，返回的考试结果摘要。包含已完成（passed, failed等）和待阅卷（pending_grading）状态"
          properties:
            score:
              type: number
              format: float
              nullable: true
              description: "最终得分。pending_grading状态时为null，前端可显示为'--'"
            completedAt:
              type: string
              format: date-time
              description: 完成考试的时间
        bookingDetails:
          type: object
          nullable: true
          description: "当考试为已报名的线下考试时，返回的报名详情"
          properties:
            bookingId:
              type: string
              format: uuid
            venueName:
              type: string
              description: 报名的考点名称
            scheduleStartTime:
              type: string
              format: date-time
              description: 报名的场次开始时间

    ExamAttempt:
      type: object
      properties:
        id:
          type: string
          format: uuid
        examName:
          type: string
        examType:
          $ref: '#/components/schemas/ExamType'
        completedAt:
          type: string
          format: date-time
        score:
          type: number
          format: float
        status:
          type: string
          enum: [passed, failed, pending_grading]
          description: 成绩状态

    OfflineVenueSchedule:
      type: object
      properties:
        venueId:
          type: string
          format: uuid
        venueName:
          type: string
        schedules:
          type: array
          items:
            type: object
            properties:
              scheduleId:
                type: string
                format: uuid
              startTime:
                type: string
                format: date-time
              endTime:
                type: string
                format: date-time
              totalSlots:
                type: integer
              availableSlots:
                type: integer
              venueAddress:
                type: string
                description: 考场详细地址
              contactPerson:
                type: string
                description: 考场联系人
              contactPhone:
                type: string
                description: 考场联系电话
              notes:
                type: string
                description: 考场备注信息
                nullable: true
      
    OfflineBooking:
      type: object
      properties:
        bookingId:
          type: string
          format: uuid
        examName:
          type: string
        venueName:
          type: string
        startTime:
          type: string
          format: date-time

    Certificate:           
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: '接种门诊上岗资格证'
        status:
          type: string
          enum: [pending_approval, valid, expired, revoked]
        imageUrl:
          type: string
          format: uri
        issueDate:
          type: string
          format: date
        expiryDate:
          type: string
          format: date

    # --- Utility Schemas ---
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
        message:
          type: string
          description: 错误信息描述
      required:
        - code
        - message

  parameters:
    Page:
      name: page
      in: query
      description: 页码，从1开始
      schema:
        type: integer
        default: 1
    PageSize:
      name: pageSize
      in: query
      description: 每页数量
      schema:
        type: integer
        default: 10

  responses:
    BadRequest:
      description: 无效的请求参数。
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: 未授权，Token无效或过期。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: 请求的资源不存在。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 在请求头的 Authorization 字段中添加 "Bearer {token}"。