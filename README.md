# 疾控医护任职资格考试系统 - 微信小程序

> 专为疾控机构医护人员设计的任职资格考试及学习辅助平台

## 📋 项目概述

本项目是一款专业的疾控医护任职资格考试系统微信小程序，提供从学习备考、模拟练习、考试报名、在线考试到证书管理的全流程服务。

### 🎯 核心功能

- **用户认证**: 微信授权登录、个人资料提交、机构审核
- **信息中心**: 公告、政策法规、重要通知展示
- **学习中心**: 题库分类练习、答案解析、学习统计
- **考试中心**: 线上考试（人脸识别、防作弊）、线下考试报名
- **个人中心**: 个人信息管理、证书查看、历史记录

## 🛠️ 技术栈

- **跨端框架**: uniapp ≥ 3.0.0
- **前端框架**: Vue 3 (Composition API) ≥ 3.3.0
- **编程语言**: TypeScript ≥ 4.9.0
- **UI组件库**: uv-ui ≥ 1.0.0
- **状态管理**: Pinia ≥ 2.1.0
- **HTTP请求**: luch-request ≥ 3.1.0
- **开发工具**: HBuilderX

## 📁 项目结构

```
src/
├── api/                    # API 接口封装
│   └── modules/            # 模块化API
├── components/             # 全局公共组件
│   ├── common/             # 通用原子组件
│   └── business/           # 业务区块组件
├── stores/                 # Pinia 状态管理
│   └── modules/            # 模块化 store
├── styles/                 # 全局样式与变量
├── types/                  # TypeScript 类型定义
└── utils/                  # 工具函数
pages/                      # 页面文件
├── login/                  # 登录页面
├── info/                   # 信息中心
├── study/                  # 学习中心
├── exam/                   # 考试中心
└── profile/                # 个人中心
static/                     # 静态资源
└── tabbar/                 # 底部导航图标
```

## 🚀 快速开始

### 环境要求

- Node.js ≥ 16.0.0
- HBuilderX (推荐最新版本)
- 微信开发者工具

### 安装依赖

```bash
npm install
```

### 开发调试

1. 使用 HBuilderX 打开项目
2. 点击"运行" → "运行到小程序模拟器" → "微信开发者工具"
3. 在微信开发者工具中预览和调试

### 构建发布

```bash
# 构建微信小程序
npm run build:mp-weixin
```

## 📝 开发规范

### 代码规范

- 严格遵循 ESLint + Prettier 代码规范
- 使用 TypeScript，禁用 `any` 类型
- 组件必须使用 `<script setup lang="ts">` 语法
- 遵循 Vue 3 Composition API 最佳实践

### 命名规范

- **目录**: `kebab-case` (如: `user-center`)
- **页面文件**: `kebab-case` (如: `order-list.vue`)
- **组件文件**: `PascalCase` (如: `UserProfile.vue`)
- **工具文件**: `camelCase` (如: `httpUtils.ts`)

### Git 提交规范

遵循 Conventional Commits 规范：

```
<type>(<scope>): <subject>

feat(user): add user profile page
fix(exam): resolve exam timer issue
docs(readme): update installation guide
```

## 🎨 UI 设计规范

### 色彩方案

- **主色调**: 蓝绿色系，体现疾控机构专业形象
- **主蓝色**: #1976d2
- **主绿色**: #4caf50
- **成功色**: #4caf50
- **警告色**: #ff9800
- **错误色**: #f44336

### 组件使用

- 优先使用 uv-ui 组件库
- 自定义组件遵循 easycom 规范
- 保持设计风格统一

## 📱 功能模块

### 模块1: 用户认证系统
- 微信授权登录
- 个人资料提交（含照片上传）
- 机构审核状态管理
- 权限控制

### 模块2: 信息中心
- 公告列表展示
- 政策法规查看
- 重要通知推送
- 内容详情页面

### 模块3: 学习中心
- 题库分类选择
- 多题型练习（单选/多选/判断/问答）
- 即时答案解析
- 练习次数限制

### 模块4: 考试中心
- 线上考试流程（人脸识别、防作弊）
- 线下考试报名管理
- 考试历史记录
- 成绩查询

### 模块5: 个人中心
- 个人信息展示
- 证书管理
- 投诉建议
- 关于我们

## 🔧 配置说明

### 环境配置

在 `src/utils/request.ts` 中配置 API 基础地址：

```typescript
const http = new Request({
  baseURL: 'https://api.cdcexam.com/v1', // 替换为实际API地址
  // ...
});
```

### 微信小程序配置

在 `manifest.json` 中配置小程序 AppID：

```json
{
  "mp-weixin": {
    "appid": "your-miniprogram-appid"
  }
}
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

*最后更新时间: 2025-06-16*
