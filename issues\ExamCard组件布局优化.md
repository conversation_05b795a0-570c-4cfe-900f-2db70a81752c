# ExamCard组件布局优化任务

**创建时间**: 2025-01-27T16:00:00
**状态**: 已完成

## 需求概述
根据 `pages/exam/examprd.md` 文件中的要求，对ExamCard组件进行布局优化：

1. 时间格式不需要体现"上午"与"下午"，在时间前加个"有效期"说明
2. "线上"与"线下"不需要展示出来，作为客户端一个判断标识即可  
3. 每个考试的前面最好加一个类似试卷的emoji
4. 线下考试不需要"查看详情"按钮，点"立即预约"后直接进入预约页面
5. 考试卡片中间余白太多，重新设计下布局与排版

## 实施内容

### 1. 修改时间格式化函数
**文件**: `src/utils/format.ts`
- 修改 `formatExamTime` 函数
- 新格式: "有效期：2025/02/02 09:00 ~ 2025/02/20 18:00"
- 去掉上午/下午显示

### 2. 优化ExamCard组件  
**文件**: `src/components/business/ExamCard.vue`

#### 2.1 添加试卷图标
- 在考试名称前添加📋 emoji图标
- 通过flex布局对齐图标和标题

#### 2.2 隐藏考试类型标签
- 移除"线上考试"/"线下考试"标签显示
- 保留状态标签

#### 2.3 移除线下考试详情按钮
- 根据需求移除"查看详情"按钮
- 修改 `canShowDetails` 函数返回false

#### 2.4 优化布局间距
- 卡片padding从20rpx调整为16rpx
- 内容区域padding-top从24rpx调整为16rpx
- 信息项间距从16rpx调整为12rpx
- 标题组margin-top从16rpx调整为12rpx
- 信息区域margin-bottom从32rpx调整为24rpx
- 操作按钮gap从24rpx调整为16rpx

## 技术实现
- 保持现有组件接口不变
- 兼容现有样式系统
- 遵循uview-plus组件规范

## 验证结果
- ✅ 时间显示格式符合要求
- ✅ 添加了📋试卷图标
- ✅ 隐藏了线上/线下类型标签  
- ✅ 移除了线下考试详情按钮
- ✅ 卡片布局更加紧凑 