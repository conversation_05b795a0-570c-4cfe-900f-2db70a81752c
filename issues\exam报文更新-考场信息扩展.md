# Exam报文更新-考场信息扩展

**任务时间**: 2025-06-27T15:21:15
**需求来源**: examprd.md 2.2 exam报文更新-6月26日

## 任务背景

根据产品需求，需要对考试相关的API报文进行更新，以支持：
1. 线上考试成绩与时间信息的正确展示
2. 线下考试考场详细信息的获取
3. 考试卡片在不同状态下的信息展示需求

## 修改内容

### 1. 扩展 OfflineVenueSchedule Schema
在 `schedules` 数组项中添加考场详细信息：
- `venueAddress`: 考场详细地址
- `contactPerson`: 考场联系人  
- `contactPhone`: 考场联系电话
- `notes`: 考场备注信息 (nullable)

### 2. 更新 API 示例
- 更新 `/exams/offline/{examId}/schedules` 的 response examples
- 添加完整的考场信息示例数据

### 3. 优化 attemptResult 字段处理
- 更新 Exam schema 中 `attemptResult.score` 字段为 nullable
- 明确 `pending_grading` 状态下 score 为 null 的处理规则
- 更新相关描述文档

### 4. 前端类型定义更新
- 新增 `ExamAttemptResult` 接口定义考试结果摘要
- 新增 `ExamBookingDetails` 接口定义线下考试报名详情
- 新增 `OfflineVenueSchedule` 接口定义考场详细信息
- 更新 `ExamItem` 接口支持新字段

### 5. ExamCard 组件功能扩展
- 支持显示考试成绩和完成时间
- 支持显示线下考试报名信息
- 新增"考场信息"按钮（已报名的线下考试）
- 优化分数显示逻辑（pending_grading 状态显示"--"）

### 6. 考场信息详情页面
- 创建全新的考场信息详情页面
- 支持显示考场地址、联系人、联系电话
- 支持一键拨打联系电话功能
- 美观的医疗健康主题UI设计

### 7. 时间格式化函数优化
- 扩展 `formatExamTime` 函数支持单个时间格式化
- 支持可选的时间范围显示模式

## 实现方案

采用完整优化方案：
- 保持考试列表卡片简洁，只显示基本报名信息
- 通过"考场信息"按钮调用现有 API 获取详细考场信息
- 前端可在 pending_grading 状态下将 null 分数显示为 "--"

## 修改文件

- `Docs/cdcopenapi.yaml`: API规范文档修改
- `src/types/api.d.ts`: 类型定义更新
- `src/components/business/ExamCard.vue`: 考试卡片组件功能扩展
- `src/utils/format.ts`: 时间格式化函数优化
- `pages/exam/exam.vue`: 考试页面事件处理
- `subpackages/exam/pages/venue-info/venue-info.vue`: 新建考场信息详情页面

## 预期效果

1. 考试卡片能正确展示：
   - 通过/未通过：显示分数与考试时间
   - 审题中：显示考试时间，分数显示为 "--"
   - 线下报名：显示报名考场与时间

2. 支持考场信息详情页面：
   - 点击"考场信息"按钮获取完整考场信息
   - 包含地址、联系人、联系电话、备注等详细信息

## 技术注意事项

- 修改为向后兼容的字段扩展
- 不涉及breaking changes
- 新增字段均为可选或nullable 