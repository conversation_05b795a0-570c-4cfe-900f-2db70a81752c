# 注册页面机构职位动态选择功能

## 任务时间
- 创建时间：2025-06-19T21:09:32
- 完成时间：2025-06-19T21:09:32

## 任务背景
注册页面的隶属机构与职位原来是写死的选项，需要改为从后台API动态获取数据，并使用下拉选择器进行选择。根据swagger.yaml文档，后台已提供 `/institutions` 和 `/positions` 接口。

## 任务目标
1. 实现从后台动态获取机构和职位列表
2. 保持使用uni.showActionSheet但改为从API动态获取数据
3. 确保用户体验良好，数据加载有适当的loading状态
4. 符合TSD技术规范要求

## 实施详情

### 1. 类型定义更新
- 在 `src/types/api.d.ts` 中添加了 `Institution` 和 `Position` 接口
- 修改 `RegisterParams` 接口，将 `organization` 和 `position` 字段改为 `institutionId` 和 `positionId`

### 2. API接口实现
- 在 `src/api/modules/user.ts` 中添加了 `getInstitutions()` 和 `getPositions()` 函数
- 符合现有的HTTP请求封装规范

### 3. 注册页面功能完善
- 页面加载时自动调用API获取机构和职位数据
- 保持使用uni.showActionSheet但数据改为从API动态获取
- 添加了适当的loading状态和错误处理
- 实现了双向数据绑定，选择后自动显示对应名称，提交时传递ID

### 4. 技术实现要点
- 使用Composition API (`<script setup>`)
- 遵循TypeScript严格类型检查
- 使用computed计算属性优化性能
- 符合TSD文档的编码规范

## 关键代码变更

### API类型定义
```typescript
// src/types/api.d.ts
export interface Institution {
  id: string;
  name: string;
}

export interface Position {
  id: string;
  name: string;
}

export interface RegisterParams {
  realName: string;
  phone: string;
  idCard: string;
  institutionId: string;  // 改为ID
  positionId: string;     // 改为ID
  avatar: string;
}
```

### API接口
```typescript
// src/api/modules/user.ts
export function getInstitutions() {
  return http.get<Institution[]>('/institutions');
}

export function getPositions() {
  return http.get<Position[]>('/positions');
}
```

### 选择器实现
```typescript
// 使用uni.showActionSheet，但数据从API获取
function selectInstitution(): void {
  if (institutions.value.length === 0) {
    uni.showToast({
      title: '机构数据尚未加载',
      icon: 'none',
    });
    return;
  }
  
  uni.showActionSheet({
    itemList: institutions.value.map(item => item.name),
    success: (res) => {
      const selectedInstitution = institutions.value[res.tapIndex];
      formData.institutionId = selectedInstitution.id;
    },
  });
}
```

## 验证要点
1. 页面加载时能正确获取机构和职位数据
2. 选择器交互正常，选择后能正确显示名称
3. 表单验证正常，必填项检查有效
4. 提交时传递的是ID而不是名称
5. 网络异常时有适当的错误提示

## 性能优化
- 使用computed计算属性缓存选择器数据格式转换
- 使用Promise.all并发获取机构和职位数据
- 选择器数据按需加载，避免内存浪费

## 符合规范
- 遵循TSD技术规范的TypeScript、Vue3、组件开发约定
- 使用Setup Store模式的Pinia状态管理
- HTTP请求通过统一封装的request实例发起
- 代码通过ESLint规范检查 