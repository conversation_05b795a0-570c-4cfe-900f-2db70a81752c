# 添加机构和职位接口

## 任务时间
- 创建时间：2023-07-25T14:30:00
- 完成时间：2023-07-25T15:00:00

## 任务背景
在现有的 Swagger API 文档中，缺少获取机构列表和职位列表的接口，这些接口对于用户注册流程是必需的。

## 任务目标
添加两个新的 API 接口和相应的数据模型，以支持用户注册时选择机构和职位。

## 实施计划
1. 在 Swagger 文档中添加 `/institutions` GET 接口 - 获取机构列表
2. 在 Swagger 文档中添加 `/positions` GET 接口 - 获取职位列表
3. 添加 `Institution` 和 `Position` 数据模型
4. 为接口添加默认响应示例数据

## 实施详情
### 1. 添加接口路径
在 paths 部分添加了以下接口：
- GET `/institutions` - 获取机构列表
- GET `/positions` - 获取职位列表

两个接口都归属于 "认证与用户 (Auth & User)" 标签，与 `/profile` 保持一致。

### 2. 添加数据模型
在 components.schemas 部分添加了以下数据模型：
- `Institution` - 包含 id(uuid)、name(string)
- `Position` - 包含 id(uuid)、name(string)

### 3. 默认响应示例
- 机构列表默认返回：市疾控中心、区疾控中心
- 职位列表默认返回：预防接种医生、检验技师

## 测试要点
1. 确保新添加的接口与现有接口风格一致
2. 验证接口返回的数据格式符合前端需求
3. 确认安全配置与错误处理与现有接口保持一致 