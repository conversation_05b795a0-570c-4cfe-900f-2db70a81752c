# 移除登录后冗余用户信息获取

**修改时间：** 2025-01-09T12:00:00  
**状态：** 已完成  

## 问题描述

登录成功后立即调用 `/profile/me` 接口获取用户详细信息，这个操作意义不大，因为：

1. 登录接口已经返回用户状态，足够进行页面跳转判断
2. 增加了不必要的网络请求，影响登录性能
3. 各个页面都有自己的用户信息获取逻辑，可以按需加载

## 解决方案

采用方案1：完全移除登录成功后的 getUserInfo 调用

### 修改内容

1. **pages/login/login.vue**
   - 移除第266-275行的 getUserInfo 调用逻辑
   - 移除 getUserInfo 的导入引用
   - 保留基本用户信息设置和页面跳转逻辑

### 验证情况

确认其他页面的用户信息获取逻辑正常：
- `pages/profile/profile.vue` - 有 `loadUserProfile()` 在页面加载时获取
- `subpackages/profile/pages/personal-info/personal-info.vue` - 有 getUserInfo 调用

## 预期效果

- 登录流程更简洁高效
- 减少不必要的网络请求
- 保持页面功能正常，各页面按需获取用户详细信息 