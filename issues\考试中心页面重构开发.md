# 考试中心页面重构开发任务

**任务创建时间**: 2025-01-27T15:10:00  
**任务状态**: 待开发  
**开发模式**: 研究->构思->计划->执行->评审  

## 📋 任务需求

基于原有考试中心页面进行重构，要求：
- UI设计规范参考 cdcexamUI.md
- 代码符合 CDCExamTSD.md 技术规范  
- 报文符合 cdcopenapi.yaml 规范
- 功能参考 CDCEXAM功能流程PRD.md 4.2章节

## 🎯 实施方案

### 架构决策
1. **状态管理策略**: 采用 Pinia Store + 智能缓存，提供更好用户体验
2. **组件拆分**: ExamCard.vue + ExamHistory.vue + 主页面重构
3. **类型系统**: 扩展 api.d.ts，严格按 OpenAPI 规范
4. **路由规划**: 保持现有路由结构，优化页面跳转逻辑

## 📋 详细开发计划

### 阶段1: 类型系统扩展 (约2小时)

#### 1.1 扩展API类型定义
**目标文件**: `src/types/api.d.ts`
- [ ] 新增 `ExamUserStatus` 枚举类型，包含以下状态：
  - `not_started` | `booking_available` | `booked` | `ready_to_start` | `in_progress` | `passed` | `failed` | `failed_final` | `finished` | `pending_grading`
- [ ] 新增 `ExamAttempt` 历史记录类型
  ```typescript
  interface ExamAttempt {
    id: string;
    examId: string;
    examName: string;
    type: ExamType;
    startTime: string;
    completedTime?: string;
    score?: number;
    status: ExamUserStatus;
    isLatest: boolean;
  }
  ```
- [ ] 扩展现有 `Exam` 接口，添加缺失属性：
  - `description?: string`
  - `rules?: string[]`
  - `userStatus: ExamUserStatus`
  - `remainingTime?: number`

#### 1.2 更新API模块类型引用
**目标文件**: `src/api/modules/exam.ts`
- [ ] 更新导入类型，引入新定义的 `ExamAttempt`
- [ ] 修复 `getExamHistory` 返回类型为 `PageData<ExamAttempt>`

### 阶段2: Pinia Store架构开发 (约3小时)

#### 2.1 创建考试状态管理Store
**目标文件**: `src/stores/modules/exam.ts`
- [ ] 采用 Setup Store 模式实现
- [ ] 状态定义：
  ```typescript
  const currentExams = ref<ExamItem[]>([])
  const examHistory = ref<ExamAttempt[]>([])
  const loading = ref(false)
  const loadingExamId = ref<string | null>(null)
  const lastUpdated = ref(0)
  ```
- [ ] 智能缓存策略（5分钟防重复请求）：
  ```typescript
  const shouldRefresh = computed(() => {
    const fiveMinutes = 5 * 60 * 1000
    return Date.now() - lastUpdated.value > fiveMinutes
  })
  ```
- [ ] 核心Actions：
  - `fetchCurrentExams(forceRefresh = false)`
  - `fetchExamHistory(page = 1, pageSize = 10)`
  - `startExam(examId: string)`
  - `refreshExamData()`
  - `setLoadingExam(examId: string | null)`

### 阶段3: 组件化开发 (约4小时)

#### 3.1 ExamCard.vue 考试卡片组件
**目标文件**: `src/components/business/ExamCard.vue`
- [ ] Props接口设计：
  ```typescript
  interface Props {
    exam: ExamItem
    loading?: boolean
  }
  ```
- [ ] Emits定义：
  ```typescript
  (e: 'click', exam: ExamItem): void
  (e: 'action', examId: string, action: 'start' | 'book' | 'cancel'): void
  ```
- [ ] 功能特性：
  - 支持线上/线下考试类型差异化展示
  - 完整的状态标签和操作按钮
  - 响应式交互效果（点击反馈、加载状态）
  - 使用 uview-plus 组件 (`u-card`, `u-tag`, `u-button`)

#### 3.2 ExamHistory.vue 历史记录组件
**目标文件**: `src/components/business/ExamHistory.vue`
- [ ] Props接口设计：
  ```typescript
  interface Props {
    historyList: ExamAttempt[]
    loading?: boolean
    hasMore?: boolean
  }
  ```
- [ ] Emits定义：
  ```typescript
  (e: 'load-more'): void
  (e: 'item-click', attempt: ExamAttempt): void
  ```
- [ ] 功能特性：
  - 支持分页加载和空状态展示
  - 考试类型和状态标识
  - 点击交互和跳转逻辑
  - 使用 uview-plus 组件 (`u-list`, `u-list-item`, `u-empty`)

### 阶段4: 工具函数扩展 (约1小时)

#### 4.1 时间格式化函数
**目标文件**: `src/utils/format.ts`
- [ ] 新增 `formatExamTime` 函数：
  ```typescript
  function formatExamTime(startTime: string, endTime: string): string {
    // 智能格式化考试时间显示
    // 同一天：2025-01-27 09:00-11:00
    // 跨天：2025-01-27 09:00 - 2025-01-28 11:00
  }
  ```
- [ ] 修复导入路径兼容性问题

### 阶段5: 主页面重构 (约5小时)

#### 5.1 页面结构重构
**目标文件**: `pages/exam/exam.vue`
- [ ] 模板重构：
  - 替换原生组件为 uview-plus 组件
  - 使用 `u-refresh` 实现下拉刷新
  - 使用 `u-modal` 实现确认弹窗
  - 使用 `u-loading-page` 优化加载状态

#### 5.2 逻辑重构
- [ ] 引入 Pinia Store：
  ```typescript
  import { useExamStore } from '@/stores/modules/exam'
  import { useUserStore } from '@/stores/modules/user'
  import { storeToRefs } from 'pinia'
  
  const examStore = useExamStore()
  const userStore = useUserStore()
  const { currentExams, examHistory, loading } = storeToRefs(examStore)
  const { isApproved } = storeToRefs(userStore)
  ```
- [ ] 权限控制优化：
  - 非认证用户友好提示
  - 使用 `u-empty` 组件展示空状态
- [ ] 交互逻辑完善：
  - 确认弹窗（开始考试前）
  - 加载状态同步
  - 错误处理和重试机制

#### 5.3 样式优化
- [ ] 遵循 cdcexamUI.md 设计规范
- [ ] 使用 SCSS 变量和混入
- [ ] 响应式设计和深色模式支持（CSS媒体查询预留）

### 阶段6: 集成测试与优化 (约2小时)

#### 6.1 功能测试
- [ ] 页面加载和数据获取测试
- [ ] 组件交互和事件传递测试
- [ ] 权限控制和状态同步测试
- [ ] 下拉刷新和分页加载测试

#### 6.2 性能优化
- [ ] 组件懒加载配置
- [ ] 图片懒加载设置
- [ ] 缓存策略验证
- [ ] 内存泄漏检查

#### 6.3 兼容性测试
- [ ] 不同机型和系统版本测试
- [ ] 网络状况适应性测试
- [ ] 边界情况处理验证

## ✅ 验收标准

### 技术标准
1. **代码规范**: 严格遵循 CDCExamTSD.md 技术约定
2. **类型安全**: 100% TypeScript 覆盖，无 any 类型
3. **API对齐**: 完全符合 cdcopenapi.yaml 规范
4. **组件化**: 业务逻辑高度组件化，复用性强

### 功能标准
1. **权限控制**: 非认证用户友好引导，认证用户完整功能
2. **数据管理**: 智能缓存，避免重复请求
3. **交互体验**: 流畅的加载、确认、反馈机制
4. **错误处理**: 完善的错误提示和恢复机制

### 性能标准
1. **首屏加载**: < 2秒
2. **交互响应**: < 300ms
3. **内存占用**: 合理的内存使用和释放
4. **网络优化**: 智能缓存和请求合并

## 🔄 风险控制

### 技术风险
- **类型定义冲突**: 渐进式扩展，保持向后兼容
- **状态管理复杂度**: 采用分模块管理，逐步迁移
- **组件耦合**: 明确接口定义，单一职责原则

### 进度风险  
- **时间压缩**: 采用并行开发，优先核心功能
- **需求变更**: 预留20%缓冲时间
- **测试覆盖**: 自动化测试和人工测试并行

## 📝 后续优化规划

### 短期优化 (完成后1周内)
1. **性能监控**: 集成性能监控工具
2. **用户反馈**: 收集实际使用反馈
3. **细节打磨**: 动画效果和微交互优化

### 中期优化 (完成后1个月内)  
1. **长列表优化**: 虚拟滚动实现
2. **离线支持**: PWA能力扩展
3. **智能推荐**: 基于用户行为的考试推荐

### 长期优化 (完成后3个月内)
1. **AI辅助**: 智能学习路径推荐
2. **数据分析**: 用户行为深度分析
3. **生态扩展**: 与其他模块深度集成

---
**预计总开发时间**: 17小时  
**开发者**: Claude Sonnet 4  
**计划制定时间**: 2025-01-27T15:30:00  
**预计完成时间**: 2025-01-29T17:00:00 