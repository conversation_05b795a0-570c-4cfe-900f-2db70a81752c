import App from './App.vue';

// #ifndef VUE3
import Vue from 'vue';
import './uni.promisify.adaptor.js';

Vue.config.productionTip = false;
App.mpType = 'app';

const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue';
import pinia from './src/stores';
import uviewPlus from 'uview-plus';
import { logEnvironmentInfo } from './src/config/env';

export function createApp() {
  const app = createSSRApp(App);
  app.use(pinia);
  app.use(uviewPlus);
  
  // 初始化环境配置信息
  logEnvironmentInfo();
  
  return {
    app,
    Pinia: pinia,
  };
}
// #endif 