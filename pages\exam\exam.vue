<!--
  疾控考试系统 - 考试页面
  医疗健康主题设计 - 2025-01-27T15:30:00

  设计理念：
  - 承接登录页的医疗健康主题配色（浅蓝+白色，绿蓝渐变）
  - 卡片式设计，清晰的视觉层次和信息架构
  - 流畅的交互动画，提升用户体验
  - 完全符合uview-plus组件规范和技术约定

  核心功能：
  - 当前考试列表展示
  - 考试状态管理和操作
  - 历史考试记录查看
  - 未认证用户友好提示
-->
<template>
  <view class="exam-page">
    <!-- 背景装饰层 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle--1" />
      <view class="decoration-circle decoration-circle--2" />
      <view class="decoration-circle decoration-circle--3" />
      <view class="decoration-wave" />
    </view>



    <!-- 主内容区域 -->
    <scroll-view 
      scroll-y
      class="main-scroll"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      :refresher-background="'transparent'"
      @refresherrefresh="handleRefresh"
    >
      <!-- 非正式用户提示 -->
      <view
        v-if="!isApproved"
        class="access-denied-section"
      >
        <view class="access-denied-card">
          <view class="access-denied-icon">
            <u-icon
              name="lock"
              size="48"
              color="#f56c6c"
            />
          </view>
          <view class="access-denied-content">
            <u-text 
              text="身份待认证"
              :bold="true"
              size="18"
              color="#303133"
              align="center"
              :margin="'0 0 16rpx 0'"
            />
            <u-text 
              text="请先完善个人资料并通过机构审核后再参加考试"
              size="14"
              color="#909399"
              align="center"
              :margin="'0 0 32rpx 0'"
            />
            <u-button
              text="去认证"
              type="primary"
              shape="circle"
              size="normal"
              :custom-style="certifyButtonStyle"
              @click="goToProfile"
            />
          </view>
        </view>
      </view>
      
      <!-- 正式用户考试内容 -->
      <view
        v-else
        class="exam-content"
      >
        <!-- 本期考试区域 -->
        <view class="current-exams-section">
          <view class="section-header">
            <view class="section-title">
              <view class="title-icon">
                <u-icon
                  name="file-text"
                  size="20"
                  color="#4CAF50"
                />
              </view>
              <u-text 
                text="本期考试"
                :bold="true"
                size="18"
                color="#303133"
              />
            </view>
          </view>
          
          <!-- 考试列表 -->
          <view
            v-if="currentExams.length > 0"
            class="exam-list"
          >
            <view 
              v-for="(exam, index) in currentExams"
              :key="exam.id"
              class="exam-card-wrapper"
              :style="{ animationDelay: `${index * 100}ms` }"
            >
              <exam-card
                :exam="exam"
                :loading="loadingExamId === exam.id"
                @click="handleExamClick"
                @action="handleExamAction"
                @detail="handleExamDetail"
                @venue-info="handleVenueInfo"
              />
            </view>
          </view>
          
          <!-- 空状态 -->
          <view
            v-else-if="!loading"
            class="empty-state"
          >
            <view class="empty-icon">
              <u-icon
                name="list"
                size="64"
                color="#c0c4cc"
              />
            </view>
            <u-text 
              text="暂无待考试项"
              size="16"
              color="#909399"
              align="center"
              :margin="'16rpx 0 8rpx 0'"
            />
            <u-text 
              text="请关注最新考试安排"
              size="14"
              color="#c0c4cc"
              align="center"
            />
          </view>
          
          <!-- 加载状态 -->
          <view
            v-if="loading"
            class="loading-state"
          >
            <u-loading-page 
              :loading="true"
              loading-text="加载考试信息中..."
              :bg-color="'transparent'"
              loading-color="#4CAF50"
            />
          </view>
        </view>
        
        <!-- 历史记录区域 -->
        <view class="history-section">
          <view
            class="section-header"
            @click="goToHistory"
          >
            <view class="section-title">
              <view class="title-icon">
                <u-icon
                  name="clock"
                  size="20"
                  color="#2196F3"
                />
              </view>
              <u-text 
                text="考试记录"
                :bold="true"
                size="18"
                color="#303133"
              />
            </view>
            <view class="section-action">
              <u-text 
                text="查看全部"
                size="14"
                color="#409eff"
              />
              <u-icon
                name="arrow-right"
                size="14"
                color="#409eff"
              />
            </view>
          </view>
          
          <!-- 历史记录组件 -->
          <exam-history
            :history-list="examHistory.slice(0, 5)"
            :loading="historyLoading"
            :has-more="examHistory.length >= 5"
            @item-click="handleHistoryItemClick"
          />
        </view>
      </view>
    </scroll-view>
    
    <!-- 确认操作模态框 -->
    <u-modal
      v-model:show="showConfirmModal"
      title="确认操作"
      :content="confirmContent"
      :show-cancel-button="true"
      confirm-color="#4CAF50"
      cancel-color="#909399"
      @confirm="handleConfirmAction"
      @cancel="handleCancelAction"
    />
    
    <!-- 考试详情弹窗 -->
    <u-popup
      v-model:show="showExamDetailPopup"
      mode="bottom"
      :border-radius="24"
      height="70%"
      :safe-area-inset-bottom="true"
    >
      <view class="exam-detail-popup">
        <view class="popup-header">
          <view class="popup-title">
            <u-text 
              text="考试详情"
              :bold="true"
              size="18"
              color="#303133"
            />
          </view>
          <u-button
            text=""
            type="info"
            plain
            size="small"
            shape="circle"
            icon="close"
            :custom-style="closeButtonStyle"
            @click="showExamDetailPopup = false"
          />
        </view>
        
        <view class="popup-content">
          <u-text 
            text="考试详情内容加载中..."
            size="14"
            color="#606266"
            align="center"
            :margin="'40rpx 0'"
          />
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '../../src/stores/modules/user';
import { useExamStore } from '../../src/stores/modules/exam';
import type { ExamItem, ExamAttempt } from '../../src/types/api';
import ExamCard from '../../src/components/business/ExamCard.vue';
import ExamHistory from '../../src/components/business/ExamHistory.vue';

// ==================== Store ====================
const userStore = useUserStore();
const examStore = useExamStore();

// 用户状态
const { isApproved } = storeToRefs(userStore);

// 考试状态
const { 
  currentExams, 
  examHistory, 
  loading, 
  loadingExamId, 
  historyLoading, 
} = storeToRefs(examStore);

// ==================== 响应式数据 ====================
/** 下拉刷新状态 */
const refreshing = ref<boolean>(false);
/** 显示确认模态框 */
const showConfirmModal = ref<boolean>(false);
/** 显示考试详情弹窗 */
const showExamDetailPopup = ref<boolean>(false);
/** 确认模态框内容 */
const confirmContent = ref<string>('');
/** 待处理的操作 */
const pendingAction = ref<{
  type: 'start' | 'book' | 'cancel' | 'continue'
  examId: string
} | null>(null);

// ==================== 设计系统样式 ====================


/** 认证按钮样式 */
const certifyButtonStyle = computed(() => ({
  width: '240rpx',
  background: 'linear-gradient(135deg, #4CAF50, #2196F3)',
  boxShadow: '0 8rpx 24rpx rgba(76, 175, 80, 0.3)',
}));

/** 关闭按钮样式 */
const closeButtonStyle = computed(() => ({
  width: '60rpx',
  height: '60rpx',
  background: 'rgba(0, 0, 0, 0.05)',
  border: 'none',
}));

// ==================== 生命周期 ====================
onMounted(async () => {
  if (isApproved.value) {
    await loadInitialData();
  }
});

// ==================== 事件处理 ====================
/**
 * 加载初始数据
 */
async function loadInitialData(): Promise<void> {
  try {
    await Promise.all([
      examStore.fetchCurrentExams(),
      examStore.fetchExamHistory(1, 5, true),
    ]);
  } catch (error) {
    console.error('加载初始数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    });
  }
}

/**
 * 下拉刷新处理
 */
async function handleRefresh(): Promise<void> {
  refreshing.value = true;
  try {
    await examStore.refreshExamData();
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
      duration: 1000,
    });
  } catch (error) {
    console.error('刷新失败:', error);
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
    });
  } finally {
    refreshing.value = false;
  }
}

/**
 * 考试卡片点击处理
 */
function handleExamClick(exam: ExamItem): void {
  console.log('考试卡片点击:', exam);
  // 卡片点击的默认行为，可以展示更多信息
}

/**
 * 考试操作处理
 */
function handleExamAction(examId: string, action: 'start' | 'book' | 'cancel' | 'continue'): void {
  const exam = currentExams.value.find(e => e.id === examId);
  if (!exam) return;
  
  let content = '';
  switch (action) {
  case 'start':
    content = `确定要开始考试"${exam.name}"吗？\n开始后请在规定时间内完成。`;
    break;
  case 'continue':
    content = `继续进行考试"${exam.name}"\n请确保网络连接稳定。`;
    break;
  case 'book':
    content = `确定要预约线下考试"${exam.name}"吗？\n预约后请按时参加考试。`;
    break;
  case 'cancel':
    content = `确定要取消预约"${exam.name}"吗？\n取消后需要重新预约。`;
    break;
  }
  
  confirmContent.value = content;
  pendingAction.value = { type: action, examId };
  showConfirmModal.value = true;
}

/**
 * 考试详情处理
 */
function handleExamDetail(examId: string): void {
  showExamDetailPopup.value = true;
  // TODO: 加载具体的考试详情
  console.log('查看考试详情:', examId);
}

/**
 * 考场信息处理
 */
function handleVenueInfo(examId: string): void {
  console.log('查看考场信息:', examId);
  // 跳转到考场信息页面
  uni.navigateTo({
    url: `/subpackages/exam/pages/venue-info/venue-info?examId=${examId}`,
  });
}

/**
 * 确认操作
 */
async function handleConfirmAction(): Promise<void> {
  showConfirmModal.value = false;
  
  if (!pendingAction.value) return;
  
  const { type, examId } = pendingAction.value;
  
  try {
    examStore.setLoadingExam(examId);
    
    switch (type) {
    case 'start':
    case 'continue':
      await startOnlineExam(examId);
      break;
    case 'book':
      await bookOfflineExam(examId);
      break;
    case 'cancel':
      await cancelOfflineExam(examId);
      break;
    }
  } catch (error) {
    console.error('操作失败:', error);
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    });
  } finally {
    examStore.setLoadingExam(null);
    pendingAction.value = null;
  }
}

/**
 * 取消操作
 */
function handleCancelAction(): void {
  showConfirmModal.value = false;
  pendingAction.value = null;
}

/**
 * 开始线上考试
 */
async function startOnlineExam(examId: string): Promise<void> {
  console.log('开始线上考试:', examId);
  
  // 跳转到线上考试页面
  uni.navigateTo({
    url: `/subpackages/exam/pages/online-exam/online-exam?id=${examId}`,
  });
}

/**
 * 预约线下考试
 */
async function bookOfflineExam(examId: string): Promise<void> {
  console.log('预约线下考试:', examId);
  
  // 跳转到线下考试预约页面
  uni.navigateTo({
    url: `/subpackages/exam/pages/offline-exam/offline-exam?id=${examId}`,
  });
}

/**
 * 取消线下考试预约
 */
async function cancelOfflineExam(examId: string): Promise<void> {
  console.log('取消线下考试预约:', examId);
  
  await examStore.fetchCurrentExams(true);
  
  uni.showToast({
    title: '预约已取消',
    icon: 'success',
  });
}

/**
 * 历史记录加载更多
 */
function handleHistoryLoadMore(): void {
  examStore.loadMoreHistory();
}

/**
 * 历史记录项点击
 */
function handleHistoryItemClick(attempt: ExamAttempt): void {
  console.log('历史记录点击:', attempt);
  
  // 跳转到考试详情或结果页面
  if (attempt.status === 'in_progress') {
    // 继续考试
    if (attempt.type === 'online') {
      uni.navigateTo({
        url: `/subpackages/exam/pages/online-exam/online-exam?id=${attempt.examId}&attemptId=${attempt.id}`,
      });
    }
  } else {
    // 查看考试结果
    uni.navigateTo({
      url: `/subpackages/exam/pages/history/history?id=${attempt.id}`,
    });
  }
}

/**
 * 跳转到个人中心
 */
function goToProfile(): void {
  uni.switchTab({ 
    url: '/pages/profile/profile', 
  });
}

/**
 * 跳转到历史记录
 */
function goToHistory(): void {
  uni.navigateTo({ 
    url: '/subpackages/exam/pages/history/history', 
  });
}
</script>

<style lang="scss" scoped>
/*
  疾控考试系统考试页面样式
  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变
  更新时间：2025-01-27T15:30:00
*/

/* ==================== 页面基础设置 ==================== */
.exam-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 25%, #e8f5e8 50%, #f3e5f5 75%, #e1f5fe 100%);
  position: relative;
  overflow: hidden;
}

/* ==================== 背景装饰 ==================== */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  
  &--1 {
    width: 400rpx;
    height: 400rpx;
    top: -200rpx;
    right: -200rpx;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.06), rgba(33, 150, 243, 0.04));
    animation: float 20s ease-in-out infinite;
  }

  &--2 {
    width: 300rpx;
    height: 300rpx;
    bottom: 15%;
    left: -150rpx;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.04), rgba(156, 39, 176, 0.03));
    animation: float 25s ease-in-out infinite reverse;
  }

  &--3 {
    width: 200rpx;
    height: 200rpx;
    top: 40%;
    left: 10%;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.03), rgba(33, 150, 243, 0.02));
    animation: float 30s ease-in-out infinite;
  }
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.03));
  border-radius: 50% 50% 0 0;
  transform: scale(1.2);
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}



 /* ==================== 主内容区域 ==================== */
 .main-scroll {
   flex: 1;
   height: 100vh;
   position: relative;
   z-index: 5;
 }

/* ==================== 访问拒绝区域 ==================== */
.access-denied-section {
  padding: 80rpx 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.access-denied-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 80rpx 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 
    0 16rpx 40rpx rgba(76, 175, 80, 0.15),
    0 8rpx 24rpx rgba(33, 150, 243, 0.08);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(76, 175, 80, 0.1);
}

.access-denied-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.1), rgba(255, 193, 7, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.access-denied-content {
  text-align: center;
  max-width: 400rpx;
}

/* ==================== 考试内容区域 ==================== */
.exam-content {
  padding: 40rpx;
  position: relative;
}



/* ==================== 区域标题 ==================== */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 8rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.title-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

 /* ==================== 本期考试列表 ==================== */
 .current-exams-section {
   margin-bottom: 48rpx;
 }

.exam-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.exam-card-wrapper {
  animation: slideInUp 0.6s ease-out both;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 24rpx;
  border: 2rpx dashed rgba(76, 175, 80, 0.2);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(192, 196, 204, 0.1), rgba(144, 147, 153, 0.05));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

/* ==================== 加载状态 ==================== */
.loading-state {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
}

/* ==================== 历史记录区域 ==================== */
.history-section {
  margin-bottom: 40rpx;
}

/* ==================== 考试详情弹窗 ==================== */
.exam-detail-popup {
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.1);
}

.popup-title {
  flex: 1;
}

.popup-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20rpx 0;
}

/* ==================== 动画效果 ==================== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== uview-plus组件样式定制 ==================== */
:deep(.u-loading-page) {
  background: transparent !important;
}

:deep(.u-modal__content) {
  border-radius: 24rpx !important;
  overflow: hidden;
}

:deep(.u-popup) {
  border-radius: 24rpx 24rpx 0 0 !important;
  overflow: hidden;
}

/* ==================== 响应式适配 ==================== */
 @media screen and (max-width: 750rpx) {
   .exam-content {
     padding: 32rpx;
   }
   
   .stats-card {
     padding: 32rpx 24rpx;
   }
   
   .access-denied-card {
     padding: 60rpx 40rpx;
     margin: 0 20rpx;
   }
 }

@media screen and (max-width: 600rpx) {
  .stats-card {
    padding: 16rpx 16rpx;
  }
  
  .stats-number {
    font-size: 32rpx;
  }
  
  .stats-label {
    font-size: 22rpx;
  }
}
</style>

<!-- 全局页面样式 -->
<style>
page {
  height: 100%;
  background: transparent;
}
</style>
