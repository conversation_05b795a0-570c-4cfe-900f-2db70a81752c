## 1. 限定
1. 严格遵循CDCExamTSD.md技术规范
2. 严格遵循cdcexamUI.md设计规范
3. 严格遵循cdcopenapi.yaml接口规范
4. 严格遵循CDCEXAM功能流程PRD.md产品需求

## 2. 修改内容
### 2.1 examcard优化布局-6月25日
1. 时间格式不需要体现“上午”与“下午”，在时间前加个“有效期”说明；
2. "线上"与“线下”不需要展示出来，作为客户端一个判断标识即可；
3. 每个考试的前面最好加一个类似试卷的emoji；
4. 线下考试 不需要“查看详情” 按钮，点“立即预约” 后直接进入预约页面，预约界面再显示地点，时间，报名人数等情况；
5. 考试卡片中间余白太多，重新设计下布局与排版。

### 2.2 exam报文更新-6月26日
1. 报文新增线上考试的成绩与时间，成功报名的线下考试的时间与地点，修改相关代码匹配新的报文；报文见 @cdcopenapi.yaml 文件
2. 考试卡片信息按新的报文进行展示，考试通过、未通过时显示 分数与考试时间；审题中时显示 考试时间，分数以 “--” 展示； 线下报名后展示报名的考场与时间，同时报名成功需要有地方展示考场地址、联系人等后端传递的消息。
3. 确认下这点击“考试中心” 时，examcard的卡片默认页面是否是“无考试信息”。如果在手机比较慢的情况下，先展示“无考试信息”再展示考试列表效果不好。

### 2.3 考试中心界面优化-0702
1. 考试卡片 “考试状态” 与有效期中间留白太多。
2. 考试成绩、完成考试时间、考场信息内容在 有效期 的下方不符合大众逻辑，需要将这些内容放在有效期的上方。
