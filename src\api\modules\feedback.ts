/**
 * 反馈管理相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';

/** 反馈类型 */
export interface FeedbackItem {
  id: string;
  type: string;
  content: string;
  contactInfo?: string;
  images?: string[];
  status: 'pending' | 'processing' | 'replied' | 'closed';
  submitTime: string;
  replyTime?: string;
  replyContent?: string;
}

/** 提交反馈参数 */
export interface SubmitFeedbackParams {
  type: string;
  content: string;
  contactInfo?: string;
  images?: string[];
}

/**
 * 提交反馈
 */
export function submitFeedback(params: SubmitFeedbackParams) {
  return http.post<boolean>('/feedback', params);
}

/**
 * 获取反馈历史
 */
export function getFeedbackHistory() {
  return http.get<FeedbackItem[]>('/feedback/history');
}

/**
 * 上传反馈图片
 */
export function uploadFeedbackImage(imageData: string) {
  return http.post<{ imageUrl: string }>('/feedback/upload-image', { imageData });
}

/**
 * 获取反馈详情
 */
export function getFeedbackDetail(feedbackId: string) {
  return http.get<FeedbackItem>(`/feedback/detail/${feedbackId}`);
}

/**
 * 删除反馈
 */
export function deleteFeedback(feedbackId: string) {
  return http.delete<boolean>(`/feedback/${feedbackId}`);
}

/**
 * 获取应用信息 (关于我们)
 */
export function getAppInfo() {
  return http.get<{
    appName: string;
    version: string;
    description: string;
    copyright: string;
  }>('/app/info');
}
