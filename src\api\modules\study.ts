/**
 * 学习中心相关API接口
 * 更新时间：2025-06-22T01:57:40
 * 修复：API路径与cdcopenapi0620-2.yaml规范完全对齐
 */
import http from '../../utils/request';
import type { 
  QuestionBankCategory, 
  QuestionWithSolution, 
  PracticeSessionRequest, 
  PracticeSessionResult, 
  PracticeAnswerRequest, 
  PracticeAnswerResult,
  PracticeStats,
} from '../../types/api';

/**
 * 获取题库分类列表
 * API规范路径：/learning/question-bank/categories
 */
export function getQuestionCategories() {
  return http.get<QuestionBankCategory[]>('/learning/question-bank/categories');
}

/**
 * 获取练习题目
 * API规范路径：/learning/question-bank/practice-questions
 * @param categoryId 题库分类ID
 * @param count 题目数量，默认10题
 */
export function getPracticeQuestions(categoryId: string, count = 10) {
  return http.get<QuestionWithSolution[]>('/learning/question-bank/practice-questions', {
    data: {
      categoryId,
      count,
    },
  });
}

/**
 * 开始练习会话 (兼容旧接口名称)
 * @deprecated 请使用 getPracticeQuestions，API规范已更新为GET请求
 */
export function startPracticeSession(categoryId: string, count = 10) {
  return getPracticeQuestions(categoryId, count);
}

/**
 * 获取用户练习统计
 * API规范路径：/learning/question-bank/stats
 */
export function getPracticeStats() {
  return http.get<PracticeStats>('/learning/question-bank/stats');
}

/**
 * 提交练习答案
 * API规范路径：/learning/question-bank/submit-answer
 * @param request 答案提交请求
 */
export function submitPracticeResult(request: PracticeAnswerRequest) {
  return http.post<PracticeAnswerResult>('/learning/question-bank/submit-answer', request);
}
