/**
 * 用户相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';
import type { 
  UserInfo, 
  LoginParams, 
  ProfileSubmissionRequest, 
  WxLoginData, 
  Institution, 
  Position,
  UserStats,
} from '../../types/api';

/**
 * 微信登录 - 使用统一包裹结构处理
 */
export function wxLogin(params: LoginParams) {
  return http.post<WxLoginData>('/auth/wechat-login', params);
}

/**
 * 获取机构列表
 */
export function getInstitutions() {
  return http.get<Institution[]>('/institutions');
}

/**
 * 获取职位列表
 */
export function getPositions() {
  return http.get<Position[]>('/positions');
}

/**
 * 提交用户注册信息
 * 注意：此接口需要使用multipart/form-data格式上传，包含照片文件
 */
export function submitUserInfo(params: ProfileSubmissionRequest) {
  return http.upload<boolean>('/profile', params);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>('/profile/me');
}

/**
 * 上传用户头像
 */
export function uploadAvatar(file: File) {
  return http.upload<string>('/user/upload-avatar', { file });
}

/**
 * 获取用户统计数据
 */
export function getUserStats() {
  return http.get<UserStats>('/profile/stats');
}
