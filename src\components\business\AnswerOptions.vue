<template>
  <view class="options-list">
    <!-- 判断题特殊处理 -->
    <template v-if="question.type === 'judgment' || question.type === 'judge'">
      <view
        class="option-item"
        :class="{ selected: selectedAnswers.includes('true') }"
        @tap="handleSelectOption('true')"
      >
        <view class="option-key"> A </view>
        <view class="option-text">
          <u-text
            text="正确"
            size="13"
            color="#303133"
            line-height="1.5"
            word-wrap="break-word"
            :custom-style="{
              wordWrap: 'break-word',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%',
            }"
          />
        </view>
      </view>
      <view
        class="option-item"
        :class="{ selected: selectedAnswers.includes('false') }"
        @tap="handleSelectOption('false')"
      >
        <view class="option-key"> B </view>
        <view class="option-text">
          <u-text
            text="错误"
            size="13"
            color="#303133"
            line-height="1.5"
            word-wrap="break-word"
            :custom-style="{
              wordWrap: 'break-word',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%',
            }"
          />
        </view>
      </view>
    </template>

    <!-- 问答题特殊处理 -->
    <template v-else-if="question.type === 'essay'">
      <view class="essay-input-container">
        <u-textarea
          :model-value="essayAnswer"
          placeholder="请在此输入您的答案..."
          :maxlength="1000"
          :show-confirm-bar="false"
          :auto-height="true"
          :height="160"
          border-radius="6rpx"
          @input="handleEssayInput"
        />
        <view class="char-count">
          <u-text :text="`${essayAnswer.length}/1000`" size="10" color="#909399" word-wrap="break-word" />
        </view>
      </view>
    </template>

    <!-- 选择题正常处理 -->
    <template v-else>
      <view
        v-for="(option, index) in question.options"
        :key="option.key || index"
        class="option-item"
        :class="{ selected: selectedAnswers.includes(option.key || String.fromCharCode(65 + index)) }"
        @tap="handleSelectOption(option.key || String.fromCharCode(65 + index))"
      >
        <view class="option-key">
          {{ option.key || String.fromCharCode(65 + index) }}
        </view>
        <view class="option-text">
          <u-text
            :text="getOptionText(option)"
            size="13"
            color="#303133"
            line-height="1.5"
            word-wrap="break-word"
            :custom-style="{
              wordWrap: 'break-word',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%',
            }"
          />
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { QuestionForDisplay } from '@/src/types/api';

interface Props {
  question: QuestionForDisplay;
  selectedAnswers: string[];
}

defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-option', optionKey: string): void;
  (e: 'essay-input', value: string): void;
}>();

const essayAnswer = ref('');

function handleSelectOption(optionKey: string) {
  emit('select-option', optionKey);
}

function handleEssayInput(value: string) {
  essayAnswer.value = value;
  emit('essay-input', value);
}

function getOptionText(option: { key?: string; value?: string | boolean } | string) {
  if (typeof option === 'object' && option.value) {
    if (option.value === 'true' || option.value === 'True') {
      return '正确';
    } else if (option.value === 'false' || option.value === 'False') {
      return '错误';
    }
    return option.value;
  }

  if (typeof option === 'string') {
    if (option === 'true' || option === 'True') {
      return '正确';
    } else if (option === 'false' || option === 'False') {
      return '错误';
    }
    return option;
  }

  return option?.value || option || '';
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.options-list {
  margin-top: 24rpx;
  padding: 0 20rpx;

  .essay-input-container {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    border: 1rpx solid #e0e6ed;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #5470c6;
      box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.1);
    }

    .char-count {
      display: flex;
      justify-content: flex-end;
      margin-top: 12rpx;
      padding-top: 8rpx;
      border-top: 1rpx solid #f0f2f5;
    }
  }

  .option-item {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 1rpx solid #e0e6ed;
    border-radius: 16rpx;
    padding: 20rpx 24rpx;
    margin-bottom: 16rpx;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 68rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: transparent;
      transition: all 0.2s ease;
    }

    @media (max-width: 320px) {
      padding: 16rpx 20rpx;
      margin-bottom: 12rpx;
      min-height: 60rpx;
    }

    &:hover {
      border-color: #5470c6;

      &::before {
        background: #5470c6;
      }
    }

    &.selected {
      border-color: #5470c6;
      background: #f6f8ff;

      &::before {
        background: #5470c6;
      }

      .option-key {
        background: #5470c6;
        color: white;
        border-color: #5470c6;
      }
    }

    .option-key {
      width: 44rpx;
      height: 44rpx;
      border-radius: 12rpx;
      background: #f8f9fa;
      border: 1rpx solid #e0e6ed;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 600;
      color: #495057;
      margin-right: 16rpx;
      flex-shrink: 0;
      transition: all 0.2s ease;
    }

    .option-text {
      flex: 1;
      display: flex;
      align-items: center;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      line-height: 1.65;
      overflow-wrap: break-word;
      hyphens: auto;
      color: #2c3e50;
      max-height: none;
      overflow: visible;
      min-height: 44rpx;

      :deep(.u-text__value) {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: pre-wrap !important;
        overflow-wrap: break-word !important;
        display: block !important;
        width: 100% !important;
        color: #2c3e50 !important;
        font-size: 30rpx !important;
        line-height: 1.65 !important;
        align-self: center !important;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .options-list {
    .essay-input-container {
      background: #1a1a1a;
      border-color: #333333;

      &:focus-within {
        border-color: #5470c6;
      }
    }

    .option-item {
      background: #1a1a1a;
      border-color: #333333;

      &:hover {
        background: #2a2a2a;
        border-color: #5470c6;
      }

      &.selected {
        background: linear-gradient(135deg, #1e2139 0%, #1a1a1a 100%);
      }

      .option-key {
        background: #2a2a2a;
        border-color: #333333;
        color: #ffffff;
      }

      .option-text {
        color: #ffffff;

        :deep(.u-text__value) {
          color: #ffffff !important;
          align-self: center !important;
        }
      }
    }
  }
}
</style>
