<template>
  <view class="camera-container">
    <view
      v-if="!cameraAuthorized"
      class="camera-unauthorized"
    >
      <u-icon
        name="camera"
        size="60"
        color="#999"
      />
      <u-text 
        text="需要摄像头权限进行人脸识别" 
        size="md" 
        color="#666" 
        align="center" 
        class="auth-tip"
      />
      <u-button
        text="开启摄像头权限"
        type="primary"
        size="medium"
        :custom-style="authButtonStyle"
        @click="handleRequestAuth"
      />
    </view>
    
    <view
      v-else
      class="camera-preview"
    >
      <camera
        v-if="shouldShowCamera"
        id="face-verification-camera"
        class="camera"
        :device-position="devicePosition"
        flash="off"
        :auto-focus="true"
        mode="normal"
        resolution="medium"
        :frame-size="frameSize"
        @initdone="onCameraReady"
        @error="onCameraError"
        @stop="onCameraStop"
        @scancode="onScanCode"
      />

      <face-guide v-if="shouldShowCamera" />
      
      <!-- 摄像头切换按钮 -->
      <view
        v-if="shouldShowCamera"
        class="camera-switch-btn"
        @click="switchCamera"
      >
        <u-icon 
          name="camera-fill" 
          size="24" 
          color="#ffffff"
        />
        <u-text 
          text="切换" 
          size="sm" 
          color="#ffffff" 
          class="switch-text"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { registerCamera, unregisterCamera, isCameraAvailable, setCameraInitialized } from '@/src/utils/globalCameraState';
import type { CameraError } from '@/src/types/api';
import FaceGuide from './FaceGuide.vue';

interface Props {
  cameraAuthorized: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'camera-ready'): void;
  (e: 'camera-error', error: CameraError): void;
  (e: 'request-auth'): void;
}>();

// 摄像头配置
const frameSize = ref('medium');
const cameraReady = ref(false);
const shouldShowCamera = ref(false);
const devicePosition = ref<'front' | 'back'>('front'); // 默认前置摄像头

/** 授权按钮样式 */
const authButtonStyle = computed(() => ({
  background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
  color: '#ffffff',
  border: 'none',
  borderRadius: '12rpx',
  boxShadow: '0 4rpx 16rpx rgba(102, 187, 106, 0.3)',
  fontSize: '28rpx',
  fontWeight: '500',
}));

function onCameraReady() {
  console.log('摄像头准备就绪');
  cameraReady.value = true;
  setCameraInitialized(true);
  emit('camera-ready');
}

function onCameraError(error: CameraError) {
  console.error('摄像头错误:', error);
  cameraReady.value = false;

  // 详细的错误处理
  let errorMessage = '摄像头初始化失败';
  if (error.errMsg) {
    if (error.errMsg.includes('auth deny') || error.errMsg.includes('permission')) {
      errorMessage = '摄像头权限被拒绝';
    } else if (error.errMsg.includes('device not found')) {
      errorMessage = '未找到摄像头设备';
    } else if (error.errMsg.includes('system error')) {
      errorMessage = '系统错误，请重启应用';
    }
  }

  console.error('摄像头错误详情:', errorMessage);
  emit('camera-error', { ...error, message: errorMessage });
}

function onCameraStop() {
  console.log('摄像头停止');
  cameraReady.value = false;
}

function onScanCode() {
  // 扫码回调，人脸识别不需要处理
}

function handleRequestAuth() {
  emit('request-auth');
}

/** 切换前后摄像头 */
function switchCamera() {
  // 给用户反馈
  uni.vibrateShort({
    type: 'light',
  });
  
  // 切换摄像头位置
  devicePosition.value = devicePosition.value === 'front' ? 'back' : 'front';
  
  uni.showToast({
    title: `已切换到${devicePosition.value === 'front' ? '前置' : '后置'}摄像头`,
    icon: 'none',
    duration: 1500,
  });
  
  console.log('摄像头已切换到:', devicePosition.value);
}

const CAMERA_ID = 'face-verification-camera';

// 监听权限变化，控制摄像头显示
function updateCameraVisibility() {
  if (props.cameraAuthorized && !shouldShowCamera.value) {
    // 检查摄像头是否可用
    if (isCameraAvailable(CAMERA_ID)) {
      // 注册摄像头组件
      if (registerCamera(CAMERA_ID)) {
        // 延迟显示摄像头，确保之前的实例完全清理
        setTimeout(() => {
          shouldShowCamera.value = true;
        }, 500);
      }
    } else {
      console.warn('摄像头不可用，可能被其他组件占用');
    }
  } else if (!props.cameraAuthorized) {
    shouldShowCamera.value = false;
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log('CameraPreview 组件挂载');
  updateCameraVisibility();
});

// 监听权限变化
watch(() => props.cameraAuthorized, () => {
  updateCameraVisibility();
});

// 组件卸载时清理摄像头资源
onUnmounted(() => {
  console.log('CameraPreview 组件卸载，清理摄像头资源');
  shouldShowCamera.value = false;
  cameraReady.value = false;
  unregisterCamera(CAMERA_ID);
});
</script>

<style lang="scss" scoped>
.camera-container {
  flex: 1;
  margin: 24rpx 0;
  position: relative;
  z-index: 10;
  min-height: 600rpx;
  height: 60vh;
  display: flex;
  flex-direction: column;
  
  .camera-unauthorized {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24rpx;
    padding: 48rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }
  
  .camera-preview {
    height: 100%;
    border-radius: 24rpx;
    overflow: hidden;
    position: relative;
    background: #000;
    
    .camera {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: block;
      background: #000;
      border-radius: 24rpx;
      overflow: hidden;
    }
    
    .camera-switch-btn {
      position: absolute;
      bottom: 32rpx;
      right: 32rpx;
      z-index: 10;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50rpx;
      padding: 16rpx 24rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;
      backdrop-filter: blur(8rpx);
      border: 1rpx solid rgba(255, 255, 255, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.95);
        background: rgba(0, 0, 0, 0.8);
      }
      
      .switch-text {
        font-size: 24rpx;
        font-weight: 500;
      }
    }
  }
}

/* 响应式设计 */
@media screen and (max-height: 600px) {
  .camera-container {
    margin: 16rpx 0;
    height: 50vh;
    min-height: 400rpx;
    
    .camera-preview {
      .camera-switch-btn {
        bottom: 24rpx;
        right: 24rpx;
        padding: 12rpx 20rpx;
        
        .switch-text {
          font-size: 22rpx;
        }
      }
    }
  }
}
</style>