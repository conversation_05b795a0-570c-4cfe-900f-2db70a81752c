<template>
  <u-card 
    :border="false"
    :shadow="2"
    :margin="'0'"
    :padding="16"
    class="exam-card"
    :class="{
      'exam-card--loading': loading,
      [`exam-card--${exam.type}`]: true,
      [`exam-card--${exam.status}`]: true
    }"
    @click="handleCardClick"
  >
    <template #head>
      <view class="exam-card__header">
        <view class="exam-card__title-group">
          <view class="exam-card__title-with-icon">
            <text class="exam-card__icon">
              📋
            </text>
            <u-text 
              :text="exam.name" 
              :bold="true" 
              size="16"
              color="#303133"
              :lines="2"
            />
          </view>
          <!-- 将状态标签放在标题右侧，形成一行布局 -->
          <view class="exam-card__status-inline">
            <u-tag 
              :text="getStatusText(exam.status)"
              :type="getStatusTagType(exam.status)"
              size="mini"
              shape="circle"
            />
          </view>
        </view>
      </view>
    </template>
    
    <template #body>
      <view class="exam-card__content">
        <!-- 考试信息 - 按优先级重新组织 -->
        <view class="exam-card__info">
          <!-- 第一优先级：基础考试信息 -->
          <view
            v-if="hasBasicInfo"
            class="exam-card__info-group exam-card__info-group--primary"
          >
            <!-- 线下考试时间 -->
            <view
              v-if="exam.bookingDetails"
              class="exam-card__info-item"
            >
              <u-icon
                name="calendar"
                size="14"
                color="#2979ff"
              />
              <u-text 
                :text="`考试时间：${formatExamDateTime(exam.bookingDetails.scheduleStartTime)}`"
                size="13"
                color="#606266"
              />
            </view>
            
            <!-- 考场信息 -->
            <view
              v-if="exam.bookingDetails"
              class="exam-card__info-item"
            >
              <u-icon
                name="map"
                size="14"
                color="#2979ff"
              />
              <u-text 
                :text="`考场：${exam.bookingDetails.venueName}`"
                size="13"
                color="#606266"
              />
            </view>
            
            <!-- 考试结果信息 -->
            <view
              v-if="exam.attemptResult"
              class="exam-card__info-item"
            >
              <u-icon
                name="level"
                size="14"
                color="#909399"
              />
              <u-text 
                :text="getScoreText()"
                size="13"
                color="#606266"
              />
            </view>
            
            <!-- 考试时长和题目数 -->
            <view
              v-if="exam.duration || exam.totalQuestions"
              class="exam-card__info-item"
            >
              <u-icon
                name="file-text"
                size="14"
                color="#909399"
              />
              <u-text 
                :text="getExamInfoText()"
                size="13"
                color="#606266"
              />
            </view>
          
            <!-- 及格分数 -->
            <view
              v-if="exam.passScore"
              class="exam-card__info-item"
            >
              <u-icon
                name="star"
                size="14"
                color="#909399"
              />
              <u-text 
                :text="`及格分：${exam.passScore}分`"
                size="13"
                color="#606266"
              />
            </view>
          </view>
          
          <!-- 第二优先级：考试完成时间 -->
          <view
            v-if="hasCompletedTimeInfo"
            class="exam-card__info-group exam-card__info-group--secondary"
          >
            <!-- 考试完成时间 -->
            <view
              v-if="exam.attemptResult?.completedAt"
              class="exam-card__info-item"
            >
              <u-icon
                name="clock"
                size="14"
                color="#67c23a"
              />
              <u-text 
                :text="`完成时间：${formatCompletedTime(exam.attemptResult.completedAt)}`"
                size="13"
                color="#303133"
              />
            </view>
          </view>
          
          <!-- 第三优先级：结果和状态信息 -->
          <view
            v-if="hasResultInfo"
            class="exam-card__info-group exam-card__info-group--result"
          >
            <!-- 重考信息 -->
            <view
              v-if="showRetryInfo"
              class="exam-card__info-item"
            >
              <u-icon
                name="reload"
                size="14"
                color="#ff9800"
              />
              <u-text 
                :text="getRetryInfoText()"
                size="13"
                color="#e6a23c"
              />
            </view>
          </view>
          
          <!-- 第四优先级：通用有效期（所有情况下都显示） -->
          <view
            v-if="shouldShowValidPeriod"
            class="exam-card__info-group exam-card__info-group--fallback"
          >
            <view class="exam-card__info-item">
              <u-icon
                name="calendar"
                size="14"
                color="#909399"
              />
              <u-text 
                :text="getValidPeriodText()"
                size="12"
                color="#909399"
              />
            </view>
          </view>
        </view>
      
        <!-- 操作按钮 -->
        <view class="exam-card__actions">
          <!-- 考场信息按钮 (线下考试已报名时显示) -->
          <u-button
            v-if="showVenueInfoButton(exam)"
            text="考场信息"
            type="info"
            size="small"
            shape="circle"
            class="exam-card__venue-btn"
            plain
            @click.stop="handleVenueInfoClick"
          />
        
          <u-button
            v-if="canTakeAction(exam)"
            :text="getActionText(exam)"
            :type="getActionButtonType(exam)"
            :loading="loading"
            size="small"
            shape="circle"
            class="exam-card__action-btn"
            @click.stop="handleActionClick"
          />
        </view>
      </view>
    </template>
  </u-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ExamItem } from '../../types/api';
import { formatExamTime } from '../../utils/format';

// Props定义
interface Props {
  /** 考试信息对象 */
  exam: ExamItem;
  /** 是否处于加载状态 */
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// Emits定义
const emit = defineEmits<{
  (e: 'click', exam: ExamItem): void;
  (e: 'action', examId: string, action: 'start' | 'book' | 'cancel' | 'continue'): void;
  (e: 'detail', examId: string): void;
  (e: 'venue-info', examId: string): void;
}>();

// 计算属性
const showRetryInfo = computed(() => {
  return props.exam.canRetry && 
         ['failed', 'failed_final'].includes(props.exam.status) &&
         props.exam.remainingAttempts !== undefined;
});

// 是否有基础考试信息（作为第一优先级）
const hasBasicInfo = computed(() => {
  return !!(props.exam.bookingDetails || props.exam.duration || props.exam.totalQuestions || props.exam.passScore || props.exam.attemptResult);
});

// 是否有完成时间信息（作为第二优先级）
const hasCompletedTimeInfo = computed(() => {
  return !!(props.exam.attemptResult?.completedAt);
});

// 是否有结果信息
const hasResultInfo = computed(() => {
  return !!(showRetryInfo.value);
});

// 是否应该显示通用有效期（所有情况下都显示）
const shouldShowValidPeriod = computed(() => {
  return !!(props.exam.startTime || props.exam.endTime);
});

// 考试类型文本 - 已移除显示，保留函数以防其他地方使用
function getExamTypeText(type: string) {
  return type === 'online' ? '线上考试' : '线下考试';
}

// 状态文本映射 - 完整覆盖所有 ExamUserStatus
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    not_started: '未开始',
    booking_available: '可预约',
    booked: '已预约',
    ready_to_start: '准备开始',
    in_progress: '进行中',
    completed: '已完成',
    passed: '已通过',
    failed: '未通过',
    failed_final: '最终未通过',
    finished: '已结束',
    pending_grading: '待阅卷',
    expired: '已过期',
  };
  return statusMap[status] || status;
}

// 状态标签类型 - 简化为4种颜色
function getStatusTagType(status: string) {
  const typeMap: Record<string, string> = {
    not_started: 'info',         // 中性灰
    booking_available: 'primary', // 主色蓝
    booked: 'warning',           // 警告橙
    ready_to_start: 'primary',   // 主色蓝
    in_progress: 'warning',      // 警告橙
    completed: 'info',           // 中性灰
    passed: 'success',           // 成功绿
    failed: 'warning',           // 警告橙（而非错误红）
    failed_final: 'warning',     // 警告橙（而非错误红）
    finished: 'info',            // 中性灰
    pending_grading: 'info',     // 中性灰
    expired: 'info',             // 中性灰
  };
  return typeMap[status] || 'info';
}

// 有效期文本 - 显示时间范围
function getValidPeriodText() {
  if (!props.exam.startTime || !props.exam.endTime) {
    return '时间待定';
  }
  return formatExamTime(props.exam.startTime, props.exam.endTime);
}

// 考试信息文本
function getExamInfoText() {
  const infoParts: string[] = [];
  
  if (props.exam.duration) {
    infoParts.push(`${props.exam.duration}分钟`);
  }
  
  if (props.exam.totalQuestions) {
    infoParts.push(`${props.exam.totalQuestions}题`);
  }
  
  return infoParts.length > 0 ? infoParts.join(' · ') : '详情待定';
}

// 重考信息文本
function getRetryInfoText() {
  if (props.exam.remainingAttempts !== undefined) {
    return props.exam.remainingAttempts > 0 
      ? `剩余${props.exam.remainingAttempts}次重考机会`
      : '无重考机会';
  }
  return props.exam.canRetry ? '允许重考' : '不可重考';
}

// 分数文本
function getScoreText() {
  if (!props.exam.attemptResult) return '';
  
  if (props.exam.attemptResult.score === null) {
    return '分数：--'; // pending_grading状态显示--
  }
  
  return `分数：${props.exam.attemptResult.score}分`;
}

// 格式化完成时间
function formatCompletedTime(dateTime: string) {
  return formatExamTime(dateTime, dateTime, false); // 只显示单个时间
}

// 格式化考试日期时间
function formatExamDateTime(dateTime: string) {
  return formatExamTime(dateTime, dateTime, false); // 只显示单个时间
}

// 获取分数图标颜色 - 简化为成功绿和警告橙
function getScoreIconColor() {
  if (!props.exam.attemptResult) return '#909399';
  if (props.exam.attemptResult.score === null) return '#909399';
  
  const score = props.exam.attemptResult.score;
  const passScore = props.exam.passScore || 60;
  
  return score >= passScore ? '#67c23a' : '#ff9800';
}

// 获取分数文本颜色 - 简化为成功绿和警告橙
function getScoreTextColor() {
  if (!props.exam.attemptResult) return '#606266';
  if (props.exam.attemptResult.score === null) return '#606266';
  
  const score = props.exam.attemptResult.score;
  const passScore = props.exam.passScore || 60;
  
  return score >= passScore ? '#67c23a' : '#ff9800';
}

// 是否可以执行操作
function canTakeAction(exam: ExamItem) {
  if (exam.type === 'online') {
    return ['not_started', 'ready_to_start', 'in_progress', 'failed'].includes(exam.status) &&
           (exam.status !== 'failed' || exam.canRetry);
  } else {
    return ['booking_available', 'booked'].includes(exam.status);
  }
}

// 操作按钮文本
function getActionText(exam: ExamItem) {
  if (exam.type === 'online') {
    switch (exam.status) {
    case 'not_started':
    case 'ready_to_start':
      return '开始考试';
    case 'in_progress':
      return '继续考试';
    case 'failed':
      return exam.canRetry ? '重新考试' : '开始考试';
    default:
      return '开始考试';
    }
  } else {
    switch (exam.status) {
    case 'booking_available':
      return '立即预约';
    case 'booked':
      return '取消预约';
    default:
      return '立即预约';
    }
  }
}

// 操作按钮类型 - 简化为4种颜色
function getActionButtonType(exam: ExamItem) {
  if (exam.type === 'online') {
    switch (exam.status) {
    case 'in_progress':
      return 'warning';  // 继续考试用警告橙
    case 'failed':
      return 'primary';  // 重考统一用主色蓝
    default:
      return 'primary';  // 开始考试用主色蓝
    }
  } else {
    return exam.status === 'booked' ? 'warning' : 'primary'; // 取消预约用警告橙，立即预约用主色蓝
  }
}

// 是否显示详情按钮 - 已移除线下考试详情按钮
function canShowDetails(exam: ExamItem) {
  return false; // 根据需求，线下考试不再显示详情按钮
}

// 是否显示考场信息按钮
function showVenueInfoButton(exam: ExamItem) {
  return exam.type === 'offline' && exam.status === 'booked' && exam.bookingDetails;
}

// 事件处理
function handleCardClick() {
  emit('click', props.exam);
}

function handleActionClick() {
  let action: 'start' | 'book' | 'cancel' | 'continue' = 'start';
  
  if (props.exam.type === 'online') {
    action = props.exam.status === 'in_progress' ? 'continue' : 'start';
  } else {
    action = props.exam.status === 'booked' ? 'cancel' : 'book';
  }
  
  emit('action', props.exam.id, action);
}

function handleDetailClick() {
  emit('detail', props.exam.id);
}

function handleVenueInfoClick() {
  emit('venue-info', props.exam.id);
}
</script>

<style lang="scss" scoped>
.exam-card {
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  &--loading {
    opacity: 0.7;
  }
  
  &--online {
    border-left: 4rpx solid #2979ff;
  }
  
  &--offline {
    border-left: 4rpx solid #ff9800;
  }
  
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  
  &__title-group {
    flex: 1;
    display: flex; // 使用flex布局让标题和状态标签在同一行
    align-items: flex-start;
    justify-content: space-between;
  }
  
  &__title-with-icon {
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
    flex: 1; // 占用剩余空间
  }
  
  &__status-inline {
    margin-left: 16rpx; // 与标题保持间距
    flex-shrink: 0; // 不压缩
  }
  
  &__icon {
    font-size: 32rpx;
    line-height: 1;
    margin-top: 2rpx;
  }
  
  &__content {
    padding-top: 8rpx; // 恢复适当的上间距
  }
  
  // 尝试覆盖 u-card 组件内部的默认间距
  :deep(.u-card__head) {
    margin-bottom: 0 !important;
    padding-bottom: 8rpx !important;
  }
  
  :deep(.u-card__body) {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  
  &__info {
    margin-bottom: 16rpx; // 减少下间距
  }
  
  // 信息分组样式
  &__info-group {
    &:not(:last-child) {
      margin-bottom: 16rpx;
    }
    
    // 主要信息组（基础考试信息）
    &--primary {
      .exam-card__info-item {
        margin-bottom: 8rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    // 次要信息组（完成时间）
    &--secondary {
      .exam-card__info-item {
        margin-bottom: 6rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    // 结果信息组
    &--result {
      .exam-card__info-item {
        margin-bottom: 6rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    // 备用信息组（通用有效期）
    &--fallback {
      opacity: 0.8;
      
      .exam-card__info-item {
        margin-bottom: 0;
      }
    }
  }
  
  &__info-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
    line-height: 1.4; // 改进行高
    
    // 移除默认的 margin-bottom，由父级容器控制
  }
  
  &__actions {
    display: flex;
    gap: 16rpx;
    justify-content: flex-end;
    margin-top: 8rpx; // 调整与信息区域的间距
  }
  
  &__action-btn,
  &__detail-btn,
  &__venue-btn {
    min-width: 160rpx;
  }
}

// 状态特定样式
.exam-card--passed {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.exam-card--failed {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.exam-card--failed_final {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  opacity: 0.8;
}

.exam-card--in_progress {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.exam-card--pending_grading {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.exam-card--booked {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .exam-card {
    &--passed {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    }
    
    &--failed,
    &--failed_final {
      background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
    }
    
    &--in_progress,
    &--booked {
      background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
    }
    
    &--pending_grading {
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    }
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .exam-card {
    &--passed {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    }
    
    &--failed {
      background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
    }
    
    &--in_progress {
      background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
    }
  }
}
</style> 