<template>
  <view class="answer-actions">
    <u-button 
      v-if="currentQuestionIndex > 0"
      text="上一题"
      type="info"
      size="normal"
      shape="round"
      :custom-style="{ flex: 1, marginRight: '12rpx' }"
      @click="handlePrevQuestion"
    />
    
    <u-button 
      v-if="currentQuestionIndex < totalQuestions - 1"
      text="下一题"
      type="primary"
      size="normal"
      shape="round"
      :custom-style="{ flex: 1 }"
      @click="handleNextQuestion"
    />
    
    <u-button 
      v-else
      text="提交试卷"
      type="success"
      size="normal"
      shape="round"
      :custom-style="{ flex: 1 }"
      @click="handleShowSubmitConfirm"
    />
  </view>
</template>

<script setup lang="ts">
interface Props {
  currentQuestionIndex: number;
  totalQuestions: number;
}

defineProps<Props>();

const emit = defineEmits<{
  (e: 'prev-question'): void;
  (e: 'next-question'): void;
  (e: 'show-submit-confirm'): void;
}>();

function handlePrevQuestion() {
  emit('prev-question');
}

function handleNextQuestion() {
  emit('next-question');
}

function handleShowSubmitConfirm() {
  console.log('ExamControls: 点击提交试卷按钮');
  emit('show-submit-confirm');
}
</script>

<style lang="scss" scoped>
.answer-actions {
  display: flex;
  gap: 10rpx;
  margin-bottom: 24rpx;
}
</style>