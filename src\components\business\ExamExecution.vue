<template>
  <view class="exam-execution-container">
    <!-- 固定头部 - 考试进度和计时器 -->
    <view class="exam-header-fixed">
      <view class="exam-progress-section">
        <u-text 
          :text="`第 ${currentQuestionIndex + 1} 题 / 共 ${questions.length} 题`"
          size="14"
          color="#606266"
          margin="0 0 4rpx 0"
        />
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: progressPercent + '%' }"
          />
        </view>
      </view>
      
      <view class="exam-timer">
        <u-icon
          name="clock"
          size="14"
          color="#ff6b35"
        />
        <u-text 
          :text="formatTime(remainingTime)"
          size="14"
          color="#ff6b35"
          bold
          margin="0 0 0 4rpx"
        />
      </view>
    </view>

    <!-- 可滚动内容区域 - 题目内容 -->
    <scroll-view 
      class="exam-content-scroll" 
      scroll-y 
      enhanced 
      :show-scrollbar="false"
    >
      <view class="question-container">
        <!-- 题目显示组件 -->
        <question-display :question="currentQuestion" />
        
        <!-- 答案选项组件 -->
        <answer-options 
          v-if="currentQuestion"
          :question="currentQuestion"
          :selected-answers="selectedAnswers"
          @select-option="handleSelectOption"
          @essay-input="handleEssayInput"
        />
      </view>
    </scroll-view>

    <!-- 固定底部 - 操作按钮和监控提醒 -->
    <view class="exam-footer-fixed">
      <!-- 考试控制按钮组件 -->
      <exam-controls
        :current-question-index="currentQuestionIndex"
        :total-questions="questions.length"
        @prev-question="handlePrevQuestion"
        @next-question="handleNextQuestion"
        @show-submit-confirm="handleShowSubmitConfirm"
      />

      <!-- 考试警告组件 -->
      <exam-warning />
    </view>

    <!-- 提交确认弹窗 -->
    <u-modal
      v-model:show="showSubmitModal"
      title="确认提交"
      :content="submitModalContent"
      show-cancel-button
      @confirm="handleSubmitExam"
      @cancel="showSubmitModal = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { QuestionForDisplay } from '@/src/types/api';
import { useExamUtils } from '@/src/composables/useExamUtils';
import QuestionDisplay from './QuestionDisplay.vue';
import AnswerOptions from './AnswerOptions.vue';
import ExamControls from './ExamControls.vue';
import ExamWarning from './ExamWarning.vue';

interface Props {
  attemptId?: string;
  questions: QuestionForDisplay[];
  currentQuestionIndex: number;
  selectedAnswers: string[];
  allAnswers: Record<number, string[]>;
  remainingTime: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-option', optionKey: string): void;
  (e: 'prev-question'): void;
  (e: 'next-question'): void;
  (e: 'submit-exam'): void;
  (e: 'essay-input', value: string): void;
}>();

const { formatTime, getSubmitModalContent, calculateProgressPercent } = useExamUtils();

const showSubmitModal = ref(false);

const currentQuestion = computed(() => props.questions[props.currentQuestionIndex]);
const progressPercent = computed(() => 
  calculateProgressPercent(props.currentQuestionIndex, props.questions.length),
);

const submitModalContent = computed(() => 
  getSubmitModalContent(props.questions.length, props.allAnswers),
);

function handleSelectOption(optionKey: string) {
  if (!currentQuestion.value) {
    console.warn('当前题目不存在，无法选择答案');
    return;
  }

  const questionType = currentQuestion.value.type;
  console.log('选择答案:', { questionType, optionKey, currentAnswers: props.selectedAnswers });

  emit('select-option', optionKey);
}

function handlePrevQuestion() {
  emit('prev-question');
}

function handleNextQuestion() {
  emit('next-question');
}

function handleShowSubmitConfirm() {
  console.log('ExamExecution: 收到提交试卷事件，显示确认弹窗');
  showSubmitModal.value = true;
  console.log('ExamExecution: showSubmitModal设置为:', showSubmitModal.value);
}

function handleSubmitExam() {
  console.log('ExamExecution: 用户确认提交，发送submit-exam事件');
  showSubmitModal.value = false;
  emit('submit-exam');
}

function handleEssayInput(value: string) {
  emit('essay-input', value);
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.exam-execution-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: $background-color;
  overflow: hidden;

  .exam-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 20rpx;
    min-height: 130rpx;
    background: linear-gradient(135deg, $surface-color 0%, #f8faff 100%);
    border-bottom: 1rpx solid $border-color;
    box-shadow: $shadow-light;
    
    @media (max-width: 320px) {
      padding: 20rpx 16rpx;
      min-height: 126rpx;
    }

    .exam-progress-section {
      flex: 1;
      margin-right: 16rpx;

      .progress-bar {
        height: 3rpx;
        background: rgba(64, 158, 255, 0.1);
        border-radius: 2rpx;
        overflow: hidden;
        margin-top: 2rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary-color, $primary-light);
          border-radius: 2rpx;
          transition: $transition-normal;
        }
      }
    }

    .exam-timer {
      display: flex;
      align-items: center;
      padding: 6rpx 12rpx;
      background: rgba(255, 107, 53, 0.1);
      border-radius: 12rpx;
      border: 1rpx solid rgba(255, 107, 53, 0.2);
    }
  }

  .exam-content-scroll {
    flex: 1;
    margin-top: 150rpx;
    margin-bottom: 360rpx;
    padding: 0 12rpx;

    @media (max-width: 320px) {
      margin-top: 146rpx;
      margin-bottom: 350rpx;
      padding: 0 8rpx;
    }

    .question-container {
      padding: 12rpx 0;
    }
  }

  .exam-footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 20rpx 24rpx;
    background: $surface-color;
    border-top: 1rpx solid $border-color;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
    z-index: 1000;
    min-height: 340rpx;
    
    @media (max-width: 320px) {
      padding: 16rpx 16rpx 20rpx;
      min-height: 330rpx;
    }
  }
}
</style>