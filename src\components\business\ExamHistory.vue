<template>
  <view class="exam-history">
    <!-- 历史记录列表 -->
    <u-cell-group 
      v-if="historyList.length > 0"
      :border="false"
      class="exam-history__list"
    >
      <u-cell
        v-for="record in historyList"
        :key="record.id"
        :border="true"
        :arrow="true"
        class="exam-history__item"
        :class="`exam-history__item--${record.status}`"
        @click="handleItemClick(record)"
      >
        <template #icon>
          <view class="exam-history__icon">
            <u-icon 
              :name="getIconName(record.type)"
              :color="getIconColor(record.type)"
              size="20"
            />
          </view>
        </template>
        
        <template #title>
          <view class="exam-history__title-group">
            <u-text 
              :text="record.examName"
              :bold="true"
              size="15"
              color="#303133"
              :lines="1"
            />
            <view class="exam-history__badges">
              <u-tag 
                :text="getTypeText(record.type)"
                :type="record.type === 'online' ? 'primary' : 'warning'"
                size="mini"
                shape="circle"
                plain
              />
              <u-tag 
                :text="getStatusText(record.status)"
                :type="getStatusTagType(record.status)"
                size="mini"
                shape="circle"
              />
            </view>
          </view>
        </template>
        
        <template #label>
          <view class="exam-history__note">
            <u-text 
              :text="getRecordNote(record)"
              size="13"
              color="#909399"
            />
          </view>
        </template>
        
        <template #value>
          <view class="exam-history__score">
            <u-text 
              :text="getScoreText(record)"
              :bold="true"
              size="14"
              :color="getScoreColor(record)"
            />
          </view>
        </template>
      </u-cell>
    </u-cell-group>
    
    <!-- 空状态 -->
    <u-empty 
      v-else-if="!loading"
      mode="history"
      icon="https://cdn.uviewui.com/uview/empty/history.png"
      text="暂无考试记录"
      text-color="#c0c4cc"
      text-size="14"
      icon-size="160"
      margin-top="80"
    />
    
    <!-- 加载更多状态 -->
    <view
      v-if="loading"
      class="exam-history__loading"
    >
      <u-loading-icon 
        text="加载中..."
        text-color="#909399"
        text-size="14"
        icon-color="#909399"
        vertical
      />
    </view>
    
    <!-- 没有更多数据提示 -->
    <view
      v-else-if="historyList.length > 0 && !hasMore"
      class="exam-history__no-more"
    >
      <u-divider
        text="没有更多数据了"
        text-color="#c0c4cc"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ExamAttempt } from '../../types/api';

// Props定义
interface Props {
  /** 历史记录列表 */
  historyList: ExamAttempt[];
  /** 是否处于加载状态 */
  loading?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasMore: true,
});

// Emits定义
const emit = defineEmits<{
  (e: 'load-more'): void;
  (e: 'item-click', attempt: ExamAttempt): void;
}>();

// 考试类型文本
function getTypeText(type: string) {
  return type === 'online' ? '线上' : '线下';
}

// 状态文本映射
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    not_started: '未开始',
    booking_available: '可预约',
    booked: '已预约',
    ready_to_start: '准备开始',
    in_progress: '进行中',
    completed: '已完成',
    passed: '已通过',
    failed: '未通过',
    failed_final: '最终未通过',
    finished: '已结束',
    pending_grading: '待阅卷',
    expired: '已过期',
  };
  return statusMap[status] || status;
}

// 状态标签类型
function getStatusTagType(status: string) {
  const typeMap: Record<string, string> = {
    not_started: 'info',
    booking_available: 'primary',
    booked: 'warning',
    ready_to_start: 'success',
    in_progress: 'warning',
    completed: 'info',
    passed: 'success',
    failed: 'error',
    failed_final: 'error',
    finished: 'info',
    pending_grading: 'warning',
    expired: 'error',
  };
  return typeMap[status] || 'info';
}

// 图标名称
function getIconName(type: string) {
  return type === 'online' ? 'laptop' : 'home';
}

// 图标颜色
function getIconColor(type: string) {
  return type === 'online' ? '#409eff' : '#e6a23c';
}

// 记录备注文本
function getRecordNote(record: ExamAttempt) {
  const startTime = new Date(record.startTime);
  const timeText = startTime.toLocaleDateString('zh-CN') + ' ' + 
                   startTime.toLocaleTimeString('zh-CN', { 
                     hour: '2-digit', 
                     minute: '2-digit', 
                   });
  
  if (record.completedTime) {
    const completedTime = new Date(record.completedTime);
    const duration = Math.round((completedTime.getTime() - startTime.getTime()) / 60000);
    return `${timeText} · 耗时${duration}分钟`;
  }
  
  return timeText;
}

// 分数文本
function getScoreText(record: ExamAttempt) {
  if (record.score !== undefined && record.score !== null) {
    return `${record.score}分`;
  }
  
  switch (record.status) {
  case 'pending_grading':
    return '待阅卷';
  case 'in_progress':
    return '进行中';
  case 'not_started':
    return '未开始';
  default:
    return '--';
  }
}

// 分数颜色
function getScoreColor(record: ExamAttempt) {
  if (record.score !== undefined && record.score !== null) {
    if (record.status === 'passed') {
      return '#67c23a';
    } else if (record.status === 'failed' || record.status === 'failed_final') {
      return '#f56c6c';
    } else {
      return '#606266';
    }
  }
  
  return '#909399';
}

// 项目点击处理
function handleItemClick(record: ExamAttempt) {
  emit('item-click', record);
}
</script>

<style lang="scss" scoped>
.exam-history {
  &__list {
    background-color: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
  }
  
  &__item {
    // u-cell 的自定义样式覆盖
    :deep(.u-cell) {
      padding: 24rpx 32rpx;
    }
    
    &:not(:last-child) {
      :deep(.u-cell) {
        border-bottom: 1rpx solid #f5f5f5;
      }
    }
    
    &--passed {
      :deep(.u-cell) {
        background: linear-gradient(90deg, rgba(103, 194, 58, 0.05) 0%, transparent 100%);
      }
    }
    
    &--failed,
    &--failed_final {
      :deep(.u-cell) {
        background: linear-gradient(90deg, rgba(245, 108, 108, 0.05) 0%, transparent 100%);
      }
    }
    
    &--in_progress {
      :deep(.u-cell) {
        background: linear-gradient(90deg, rgba(230, 162, 60, 0.05) 0%, transparent 100%);
      }
    }
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 72rpx;
    height: 72rpx;
    background-color: #f8f9fa;
    border-radius: 50%;
  }
  
  &__title-group {
    flex: 1;
    width: 100%;
  }
  
  &__badges {
    display: flex;
    gap: 12rpx;
    margin-top: 12rpx;
  }
  
  &__note {
    margin-top: 8rpx;
  }
  
  &__score {
    text-align: right;
    min-width: 80rpx;
  }
  
  &__loading {
    padding: 60rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  &__no-more {
    padding: 40rpx 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .exam-history {
    &__list {
      background-color: #1d1d1d;
    }
    
    &__item {
      border-bottom-color: #2d2d2d;
      
      &--passed {
        background: linear-gradient(90deg, rgba(103, 194, 58, 0.1) 0%, transparent 100%);
      }
      
      &--failed,
      &--failed_final {
        background: linear-gradient(90deg, rgba(245, 108, 108, 0.1) 0%, transparent 100%);
      }
      
      &--in_progress {
        background: linear-gradient(90deg, rgba(230, 162, 60, 0.1) 0%, transparent 100%);
      }
    }
    
    &__icon {
      background-color: #2d2d2d;
    }
  }
}
</style> 