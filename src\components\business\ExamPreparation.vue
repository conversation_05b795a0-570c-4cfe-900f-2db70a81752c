<template>
  <view class="preparation-stage">
    <!-- 背景装饰层 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle--1" />
      <view class="decoration-circle decoration-circle--2" />
      <view class="decoration-circle decoration-circle--3" />
    </view>

    <!-- 顶部：考试信息 -->
    <view class="exam-info-section">
      <view class="exam-info">
        <view class="exam-details">
          <view class="detail-item">
            <u-icon
              name="clock-fill"
              size="18"
              color="#4CAF50"
            />
            <u-text
              :text="`考试时长：${examDisplayInfo.duration}分钟`"
              size="md"
              color="#666"
            />
          </view>
          <view class="detail-item">
            <u-icon
              name="file-text-fill"
              size="18"
              color="#2196F3"
            />
            <u-text
              :text="`题目数量：${examDisplayInfo.totalQuestions}题`"
              size="md"
              color="#666"
            />
          </view>
          <view class="detail-item">
            <u-icon
              name="checkmark-circle-fill"
              size="18"
              color="#FF9800"
            />
            <u-text
              :text="`及格分数：${examDisplayInfo.passScore}分`"
              size="md"
              color="#666"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 中间：考前须知内容 -->
    <view class="exam-rules-section">
      <scroll-view 
        class="rules-scroll-view"
        scroll-y
        :scroll-top="scrollTop"
        @scroll="handleScroll"
        @scrolltolower="handleScrollToLower"
      >
        <view 
          v-if="examRules" 
          class="rules-content"
        >
          <u-parse 
            :content="examRules" 
            :lazy-load="true"
            :preview-img="true"
            :show-img-menu="true"
            :selectable="true"
            @ready="handleParseReady"
            @load="handleParseLoad"
          />
        </view>
        <view 
          v-else 
          class="rules-loading"
        >
          <u-loading-icon
            mode="spinner"
            color="#4CAF50"
          />
          <u-text
            text="加载考前须知中..."
            size="sm"
            color="#999"
          />
        </view>
      </scroll-view>
    </view>

    <!-- 底部：确认按钮区域 -->
    <view class="action-section">
      <!-- 确认按钮 -->
      <u-button
        v-if="typeof buttonText === 'string'"
        :text="buttonText"
        type="primary"
        :disabled="!canStartExam"
        :loading="false"
        :custom-style="actionButtonStyle"
        shape="circle"
        size="large"
        @click="handleStartExam"
      />
      
      <!-- 倒计时按钮 -->
      <view 
        v-else
        class="countdown-button"
        :style="actionButtonStyle"
      >
        <view class="countdown-text">
          {{ buttonText.text }}
        </view>
        <view class="countdown-number">
          {{ buttonText.countdown }}s
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch, getCurrentInstance } from 'vue';
import type { ScrollEvent } from '@/src/types/api';

interface ExamInfo {
  id: string;
  name: string;
  duration: number;
  totalQuestions: number;
  passScore: number;
}

interface ExamRulesData {
  rules: string;
  duration?: number;
  totalQuestions?: number;
  passScore?: number;
  countdownTime?: number;
}

interface Props {
  examInfo: ExamInfo;
  examRulesData: ExamRulesData | null;
}

const props = defineProps<Props>();

// 获取当前组件实例
const instance = getCurrentInstance();

// 获取考试信息，优先使用API返回的数据，回退到props传入的数据
const examDisplayInfo = computed(() => ({
  name: props.examInfo.name,
  duration: props.examRulesData?.duration ?? props.examInfo.duration,
  totalQuestions: props.examRulesData?.totalQuestions ?? props.examInfo.totalQuestions,
  passScore: props.examRulesData?.passScore ?? props.examInfo.passScore,
}));

// 获取考前须知内容
const examRules = computed(() => props.examRulesData?.rules || null);

// 获取倒计时时间
const countdownTime = computed(() => props.examRulesData?.countdownTime || 0);

const emit = defineEmits<{
  (e: 'start-exam'): void;
}>();

const scrollTop = ref(0);
const hasScrollToBottom = ref(false);
const hasScrollBar = ref(false);
const isContentReady = ref(false);

// 防抖定时器
let resizeTimer: number | null = null;
let checkScrollBarTimer: number | null = null;

// 防抖函数
function debounce<T extends (...args: unknown[]) => void>(func: T, wait: number): T {
  return ((...args: unknown[]) => {
    if (checkScrollBarTimer) {
      clearTimeout(checkScrollBarTimer);
    }
    checkScrollBarTimer = setTimeout(() => func(...args), wait);
  }) as T;
}

// 防抖的滚动条检查函数（带重试）
const debouncedCheckScrollBar = debounce(checkScrollBarWithRetry, 200);

// 添加重试机制的滚动条检查
function checkScrollBarWithRetry(retryCount = 2) {
  console.log(`尝试检查滚动条，剩余重试次数: ${retryCount}`);
  
  if (retryCount <= 0) {
    console.error('滚动条检测重试次数用完，使用保守策略');
    handleScrollBarDetectionFailure();
    return;
  }
  
  // 使用更可靠的组合查询方式
  const query = uni.createSelectorQuery().in(instance);
  
  // 先获取元素的 boundingClientRect
  query.select('.rules-scroll-view').boundingClientRect((rect) => {
    if (rect) {
      // 再获取滚动相关信息
      const scrollQuery = uni.createSelectorQuery().in(instance);
      scrollQuery.select('.rules-scroll-view').scrollOffset((scroll) => {
        console.log('滚动条查询回调执行:', { rect, scroll });
        
        if (scroll && scroll.scrollHeight !== undefined && rect.height !== undefined) {
          // 使用 scrollHeight 和 rect.height 比较
          const hasScroll = scroll.scrollHeight > rect.height;
          console.log('滚动条检测结果:', {
            scrollHeight: scroll.scrollHeight,
            clientHeight: rect.height,
            hasScroll,
            当前hasScrollBar: hasScrollBar.value,
            当前hasScrollToBottom: hasScrollToBottom.value,
          });
          
          hasScrollBar.value = hasScroll;
          
          // 只有在没有滚动条时才设置为已读完
          if (!hasScroll) {
            console.log('没有滚动条，设置为已读完');
            hasScrollToBottom.value = true;
            console.log('设置后状态:', {
              hasScrollBar: hasScrollBar.value,
              hasScrollToBottom: hasScrollToBottom.value,
              canStartExam: canStartExam.value,
            });
          } else {
            console.log('有滚动条，保持当前滚动状态:', hasScrollToBottom.value);
          }
          
          console.log('滚动条检测最终状态:', {
            scrollHeight: scroll.scrollHeight,
            clientHeight: rect.height,
            hasScrollBar: hasScrollBar.value,
            hasScrollToBottom: hasScrollToBottom.value,
            canStartExam: canStartExam.value,
          });
        } else {
          console.error('滚动信息查询失败，准备重试...', { scroll, rect });
          // 延迟重试
          setTimeout(() => {
            checkScrollBarWithRetry(retryCount - 1);
          }, 100);
        }
      }).exec();
    } else {
      console.error('元素rect查询失败，准备重试...');
      // 延迟重试
      setTimeout(() => {
        checkScrollBarWithRetry(retryCount - 1);
      }, 100);
    }
  }).exec();
}

const buttonText = computed(() => {
  if (!examRules.value) {
    return '加载考前须知中...';
  }
  
  // 内容加载中
  if (!isContentReady.value) {
    return '内容加载中...';
  }
  
  // 如果有倒计时，显示倒计时信息
  if (countdownTime.value > 0) {
    // 检查是否同时需要滚动到底部
    const needScrollToBottom = hasScrollBar.value && !hasScrollToBottom.value;
    let text = '请仔细阅读"考前须知"';
    
    if (needScrollToBottom) {
      text = '请滚动到底部完整阅读"考前须知"';
    }
    
    return { 
      text, 
      countdown: countdownTime.value, 
    };
  }
  
  // 倒计时结束后，检查是否需要滚动到底部
  if (hasScrollBar.value && !hasScrollToBottom.value) {
    return '请滚动到底部阅读完整内容';
  }
  
  return '下一步';
});

/** 按钮样式 - 医疗健康主题 */
const actionButtonStyle = computed(() => ({
  width: '100%',
  height: '88rpx',
  background: canStartExam.value
    ? '#66BB6A'
    : '#e0e0e0',
  color: '#0a0a0a',
  border: 'none',
  borderRadius: '44rpx',
  boxShadow: canStartExam.value
    ? '0 8rpx 24rpx rgba(102, 187, 106, 0.3)'
    : '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  opacity: canStartExam.value ? 1 : 0.6,
  transform: canStartExam.value ? 'translateY(0)' : 'translateY(2rpx)',
}));

const canStartExam = computed(() => {
  console.log('canStartExam计算中，当前状态:', {
    examRules: !!examRules.value,
    isContentReady: isContentReady.value,
    countdownTime: countdownTime.value,
    hasScrollBar: hasScrollBar.value,
    hasScrollToBottom: hasScrollToBottom.value,
  });
  
  // 基础条件检查
  if (!examRules.value || !isContentReady.value || countdownTime.value > 0) {
    console.log('基础条件未满足，返回false');
    return false;
  }
  
  // 如果有滚动条，需要滚动到底部
  if (hasScrollBar.value && !hasScrollToBottom.value) {
    console.log('有滚动条但未滚动到底部，返回false');
    return false;
  }
  
  console.log('所有条件满足，返回true');
  return true;
});

onMounted(() => {
  // 使用uni.onWindowResize监听窗口变化
  nextTick(() => {
    // 监听窗口尺寸变化
    uni.onWindowResize(handleWindowResize);
    
    // 初始检查
    if (examRules.value) {
      debouncedCheckScrollBar();
    }
  });
});

onUnmounted(() => {
  // 清理监听器
  uni.offWindowResize(handleWindowResize);
  
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  if (checkScrollBarTimer) {
    clearTimeout(checkScrollBarTimer);
  }
});

// 处理滚动条检测失败的情况
function handleScrollBarDetectionFailure() {
  console.warn('滚动条检测失败，使用保守策略');
  // 保守处理：假设有滚动条，不自动设置为已读完
  hasScrollBar.value = true;
  // 不重置用户的滚动状态，如果用户之前滚动过，保持状态
  if (!hasScrollToBottom.value) {
    hasScrollToBottom.value = false;
  }
  
  console.log('保守处理后状态:', {
    hasScrollBar: hasScrollBar.value,
    hasScrollToBottom: hasScrollToBottom.value,
    canStartExam: canStartExam.value,
  });
}

function handleParseReady() {
  console.log('u-parse组件ready事件触发');
  isContentReady.value = true;
  // 内容加载完成后重新检查滚动条
  nextTick(() => {
    debouncedCheckScrollBar();
  });
}

function handleParseLoad() {
  console.log('u-parse组件load事件触发');
  // 所有图片加载完成后再次检查
  nextTick(() => {
    debouncedCheckScrollBar();
  });
}

function handleWindowResize() {
  // 使用防抖避免频繁调用（降级方案）
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  resizeTimer = setTimeout(() => {
    console.log('窗口大小变化，重新检查滚动条');
    // 重置滚动状态
    hasScrollToBottom.value = false;
    debouncedCheckScrollBar();
  }, 300);
}

function handleScroll(e: ScrollEvent) {
  scrollTop.value = e.detail.scrollTop;
}

function handleScrollToLower() {
  hasScrollToBottom.value = true;
  console.log('已滚动到底部');
}

function handleStartExam() {
  if (!canStartExam.value) return;
  emit('start-exam');
}

// 监听examRules变化，重新检查滚动条
watch(() => examRules.value, (newRules) => {
  if (newRules) {
    console.log('考前须知内容已更新，重置状态');
    // 重置所有状态
    hasScrollToBottom.value = false;
    isContentReady.value = false;
    
    // 延迟检查，等待DOM更新
    nextTick(() => {
      debouncedCheckScrollBar();
    });
  }
});

// 监听倒计时变化，当倒计时结束后检查滚动条
watch(() => countdownTime.value, (newTime, oldTime) => {
  console.log(`倒计时变化: ${oldTime} -> ${newTime}`);
  console.log('当前状态:', {
    hasScrollBar: hasScrollBar.value,
    hasScrollToBottom: hasScrollToBottom.value,
    isContentReady: isContentReady.value,
    canStartExam: canStartExam.value,
  });
  if (oldTime > 0 && newTime === 0) {
    console.log('倒计时结束，检查滚动条存在性（不重置滚动状态）');
    // 倒计时结束时只检查滚动条是否存在，不重置用户的滚动进度
    nextTick(() => {
      console.log('nextTick执行，准备调用debouncedCheckScrollBar');
      debouncedCheckScrollBar();
    });
  }
});

// 监听内容就绪状态，确保检查时机准确
watch(() => isContentReady.value, (ready) => {
  if (ready) {
    console.log('内容已就绪，执行滚动条检查');
    debouncedCheckScrollBar();
  }
});
</script>

<style lang="scss" scoped>
/*
  疾控考试系统考前准备页面样式
  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变
  与login.vue保持一致的背景设计
*/

/* ==================== 页面基础设置 ==================== */
.preparation-stage {
  height: 100vh;
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ==================== 背景装饰 ==================== */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));

  &--1 {
    width: 320rpx;
    height: 320rpx;
    top: -160rpx;
    right: -160rpx;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(33, 150, 243, 0.08));
  }

  &--2 {
    width: 240rpx;
    height: 240rpx;
    bottom: 20%;
    left: -120rpx;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.06), rgba(76, 175, 80, 0.06));
  }

  &--3 {
    width: 180rpx;
    height: 180rpx;
    top: 30%;
    left: 20%;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.04), rgba(33, 150, 243, 0.04));
  }
}

/* ==================== 顶部考试信息区域 ==================== */
.exam-info-section {
  flex-shrink: 0;
  padding: 60rpx 40rpx 32rpx;
  position: relative;
  z-index: 10;

  .exam-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    .exam-title {
      margin-bottom: 24rpx;
      display: block;
      width: 100%;
    }

    .exam-details {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        padding: 8rpx 0;
      }
    }
  }
}

/* ==================== 中间考前须知区域（可滚动） ==================== */
.exam-rules-section {
  flex: 1;
  padding: 24rpx 40rpx;
  position: relative;
  z-index: 10;
  min-height: 0;

  .rules-scroll-view {
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    .rules-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      word-wrap: break-word;
      
      :deep(ol), :deep(ul) {
        padding-left: 32rpx;
        margin-bottom: 16rpx;
      }
      
      :deep(li) {
        margin-bottom: 8rpx;
      }
      
      :deep(h1), :deep(h2), :deep(h3) {
        color: #2c5aa0;
        margin: 16rpx 0 12rpx 0;
        font-weight: 600;
      }

      :deep(p) {
        margin-bottom: 12rpx;
      }

      :deep(strong) {
        color: #2c5aa0;
        font-weight: 600;
      }
    }

    .rules-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      padding: 80rpx 32rpx;
      min-height: 200rpx;
    }
  }
}

/* ==================== 底部按钮区域 ==================== */
.action-section {
  flex-shrink: 0;
  padding: 32rpx 40rpx 60rpx;
  position: relative;
  z-index: 10;
  background: transparent;

  .action-tip {
    background: rgba(255, 107, 53, 0.1);
    border-radius: 16rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 20rpx;
    border: 1rpx solid rgba(255, 107, 53, 0.2);
    backdrop-filter: blur(10rpx);
  }
}

/* 倒计时按钮样式 */
.countdown-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  cursor: default;
  padding: 0 24rpx;
  box-sizing: border-box;
  
  .countdown-text {
    font-size: 30rpx;
    color: #333333;
    font-weight: 600;
    line-height: 1.3;
    text-align: left;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 120rpx);
  }
  
  .countdown-number {
    font-size: 36rpx;
    font-weight: 700;
    color: #333333;
    line-height: 1;
    padding: 8rpx 16rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    min-width: 80rpx;
    max-width: 100rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
  }
}

/* 响应式字体调整 */
@media screen and (max-width: 750rpx) {
  .countdown-button {
    padding: 0 20rpx;
    
    .countdown-text {
      font-size: 28rpx;
      max-width: calc(100% - 100rpx);
    }
    
    .countdown-number {
      font-size: 32rpx;
      min-width: 70rpx;
      max-width: 90rpx;
      padding: 6rpx 12rpx;
    }
  }
}

@media screen and (max-width: 600rpx) {
  .countdown-button {
    gap: 8rpx;
    padding: 0 16rpx;
    
    .countdown-text {
      font-size: 26rpx;
      max-width: calc(100% - 80rpx);
    }
    
    .countdown-number {
      font-size: 30rpx;
      min-width: 60rpx;
      max-width: 80rpx;
      padding: 4rpx 10rpx;
    }
  }
}

/* ==================== uview-plus组件样式定制 ==================== */
/* 按钮激活效果 */
:deep(.u-button) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  
  &:not(.u-button--disabled):active {
    transform: translateY(2rpx) !important;
    opacity: 0.9 !important;
  }
}

/* 文本组件样式优化 */
:deep(.u-text) {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* 图标颜色渐变效果 */
:deep(.u-icon) {
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1)) !important;
}

/* ==================== 响应式设计 ==================== */
@media screen and (max-height: 600px) {
  .preparation-stage {
    .exam-info-section {
      padding: 40rpx 36rpx 24rpx;
      
      .exam-info {
        padding: 24rpx 20rpx;
      }
    }
    
    .exam-rules-section {
      padding: 20rpx 36rpx;
      
      .rules-scroll-view {
        padding: 20rpx;
      }
    }
    
    .action-section {
      padding: 24rpx 36rpx 40rpx;
      
      .action-tip {
        padding: 12rpx 16rpx;
        margin-bottom: 16rpx;
      }
    }
  }
}

@media screen and (max-height: 500px) {
  .preparation-stage {
    .exam-info-section {
      padding: 32rpx 36rpx 20rpx;
      
      .exam-info {
        padding: 20rpx 16rpx;
        
        .exam-details {
          gap: 12rpx;
          
          .detail-item {
            padding: 4rpx 0;
          }
        }
      }
    }
    
    .exam-rules-section {
      padding: 16rpx 36rpx;
      
      .rules-scroll-view {
        padding: 16rpx;
      }
    }
    
    .action-section {
      padding: 20rpx 36rpx 32rpx;
    }
  }
}
</style>