<template>
  <view class="exam-warning-multiline">
    <view class="warning-header">
      <u-icon
        name="error-circle-fill"
        color="#e6a23c"
        size="16"
      />
      <u-text
        text="考试监控提醒："
        color="#8b6914"
        size="16"
        bold
        margin="0 0 0 6rpx"
      />
    </view>
    <view class="warning-list">
      <view class="warning-item">
        <u-text
          text="• 请勿切换应用或离开考试页面"
          color="#8b6914"
          size="14"
          :margin="'0'"
        />
      </view>
      <view class="warning-item">
        <u-text
          text="• 考试过程已启用防作弊监控"
          color="#8b6914"
          size="14"
          :margin="'0'"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// No props or emits needed for this static warning component
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.exam-warning-multiline {
  padding: 18rpx 24rpx;
  background: linear-gradient(135deg, #fffbe6 0%, #fdf8e0 100%);
  border: 1rpx solid rgba(230, 162, 60, 0.3);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(230, 162, 60, 0.12);
  min-height: 140rpx;

  .warning-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    @media (max-width: 320px) {
      margin-bottom: 10rpx;
    }
  }

  .warning-list {
    padding-left: 22rpx;
    
    @media (max-width: 320px) {
      padding-left: 20rpx;
    }
    
    .warning-item {
      margin-bottom: 5px;
      line-height: 1.6;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      @media (max-width: 320px) {
        margin-bottom: 14rpx;
      }
    }
  }
}
</style>