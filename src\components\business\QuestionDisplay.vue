<template>
  <view v-if="question" class="question-container">
    <!-- 题目类型标签 -->
    <view class="question-type">
      <view class="type-tag">
        <text class="type-text">{{ getQuestionTypeText(question.type) }}</text>
      </view>
    </view>
    
    <!-- 题目内容卡片 -->
    <view class="question-card">
      <view class="question-stem">
        <u-text
          :text="question.stem"
          size="16"
          color="#2c3e50"
          line-height="1.6"
          word-wrap="break-word"
          :custom-style="{
            wordWrap: 'break-word',
            wordBreak: 'break-all',
            whiteSpace: 'pre-wrap',
            display: 'block',
            width: '100%',
            fontSize: '30rpx',
            fontWeight: '500',
            letterSpacing: '0.5rpx',
            lineHeight: '1.6'
          }"
        />
      </view>
      
      <!-- 题目图片 -->
      <view v-if="question.image" class="question-image">
        <u-image
          :src="question.image"
          width="100%"
          height="auto"
          mode="widthFix"
          border-radius="12rpx"
          :lazy-load="true"
          @click="previewImage(question.image)"
        />
        <view class="image-tip">
          <u-icon name="eye" size="20rpx" color="#8c8c8c" />
          <text class="tip-text">点击查看大图</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { QuestionForDisplay } from '@/src/types/api';

interface Props {
  question: QuestionForDisplay | null;
}

defineProps<Props>();

function previewImage(imageUrl: string) {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
}

function getQuestionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    judgment: '判断题',
    essay: '问答题',
    single: '单选题',
    multiple: '多选题',
    judge: '判断题',
  };
  return typeMap[type] || '未知题型';
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.question-container {
  padding: 0;
  margin: 0;
  background: transparent;

  .question-type {
    display: flex;
    align-items: center;
    padding: 16rpx 24rpx;
    margin-bottom: 20rpx;
    
    .type-tag {
      background: #5470c6;
      color: #ffffff;
      border-radius: 20rpx;
      padding: 8rpx 16rpx;
      font-size: 22rpx;
      font-weight: 500;
      box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.2);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-1rpx);
        box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.25);
      }
      
      .type-text {
        color: #ffffff;
        font-size: 22rpx;
        font-weight: 500;
      }
    }
  }

  .question-card {
    background: #ffffff;
    border-radius: 16rpx;
    margin: 0 20rpx 40rpx 20rpx;
    padding: 28rpx 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid #e0e6ed;
    position: relative;
    overflow: hidden;
    min-height: 120rpx;
    
    .question-stem {
      margin-bottom: 24rpx;
      line-height: 1.6;
      position: relative;
      
      :deep(.u-text__value) {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: pre-wrap !important;
        overflow-wrap: break-word !important;
        display: block !important;
        width: 100% !important;
        color: #2c3e50 !important;
        font-size: 30rpx !important;
        font-weight: 500 !important;
        letter-spacing: 0.5rpx !important;
        line-height: 1.65 !important;
      }
    }
    
    .question-image {
      position: relative;
      border-radius: 12rpx;
      overflow: hidden;
      background: #f8f9fa;
      
      :deep(.u-image) {
        border-radius: 12rpx;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.01);
        }
      }
      
      .image-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        margin-top: 12rpx;
        padding: 6rpx;
        
        .tip-text {
          font-size: 20rpx;
          color: #8c8c8c;
          font-weight: 400;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .question-container {
    .question-card {
      margin: 0 16rpx 36rpx 16rpx;
      padding: 24rpx 20rpx;
      
      .question-stem {
        :deep(.u-text__value) {
          font-size: 28rpx !important;
        }
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .question-container {
    .question-type {
      .type-tag {
        background: #5470c6;
        color: #ffffff;
      }
    }
    
    .question-card {
      background: #1a1a1a;
      border-color: #333333;
      
      .question-stem {
        :deep(.u-text__value) {
          color: #ffffff !important;
        }
      }
      
      .question-image {
        background: #2a2a2a;
        
        .image-tip {
          .tip-text {
            color: #cccccc;
          }
        }
      }
    }
  }
}
</style>