<template>
  <view class="registration-manager">
    <!-- 已报名状态 -->
    <view
      v-if="registrationInfo"
      class="registered-info"
    >
      <view class="registered-header">
        <u-icon
          name="checkmark-circle-fill"
          size="24"
          color="#4caf50"
        />
        <text class="registered-title">
          报名成功
        </text>
      </view>
      
      <view class="registered-details">
        <view class="registered-item">
          <text class="registered-label">
            报名时间：
          </text>
          <text class="registered-value">
            {{ formatDateTime(registrationInfo.registrationTime) }}
          </text>
        </view>
        
        <view class="registered-item">
          <text class="registered-label">
            准考证号：
          </text>
          <text class="registered-value">
            {{ registrationInfo.admissionNumber }}
          </text>
        </view>
        
        <view class="registered-item">
          <text class="registered-label">
            座位号：
          </text>
          <text class="registered-value">
            {{ registrationInfo.seatNumber || '待分配' }}
          </text>
        </view>
      </view>
      
      <view class="registered-actions">
        <button
          class="action-btn secondary"
          @tap="handleDownloadAdmissionTicket"
        >
          下载准考证
        </button>
        
        <button 
          v-if="canCancelRegistration"
          class="action-btn danger"
          @tap="handleShowCancelModal"
        >
          取消报名
        </button>
      </view>
    </view>

    <!-- 未报名状态 -->
    <view
      v-else
      class="registration-actions"
    >
      <view
        v-if="!canRegister"
        class="registration-disabled"
      >
        <text class="disabled-text">
          {{ getDisabledReason() }}
        </text>
      </view>
      
      <button 
        v-else
        class="register-btn"
        @tap="handleShowRegisterModal"
      >
        立即报名
      </button>
    </view>

    <!-- 报名确认弹窗 -->
    <u-modal
      v-model:show="showRegisterConfirm"
      title="确认报名"
      :content="registerModalContent"
      show-cancel-button
      @confirm="handleConfirmRegistration"
      @cancel="showRegisterConfirm = false"
    />

    <!-- 取消报名确认弹窗 -->
    <u-modal
      v-model:show="showCancelConfirm"
      title="取消报名"
      content="确定要取消报名吗？取消后需要重新报名。"
      show-cancel-button
      confirm-color="#f44336"
      @confirm="handleConfirmCancelRegistration"
      @cancel="showCancelConfirm = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { ExamDetail, ExamBookingDetails } from '@/src/types/api';

interface Props {
  examInfo: ExamDetail;
  registrationInfo: ExamBookingDetails | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'register'): void;
  (e: 'cancel-registration'): void;
  (e: 'download-admission-ticket'): void;
}>();

const showRegisterConfirm = ref(false);
const showCancelConfirm = ref(false);

const canRegister = computed(() => {
  if (!props.examInfo) return false;
  
  const now = new Date();
  const deadline = new Date(props.examInfo.registrationDeadline);
  const isFull = props.examInfo.registeredCount >= props.examInfo.maxCapacity;
  
  return now < deadline && !isFull && props.examInfo.status === 'open';
});

const canCancelRegistration = computed(() => {
  if (!props.registrationInfo || !props.examInfo) return false;
  
  const now = new Date();
  const examTime = new Date(props.examInfo.examTime);
  const hoursBeforeExam = (examTime.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  return hoursBeforeExam > 24; // 考试前24小时内不能取消
});

const registerModalContent = computed(() => {
  if (!props.examInfo) return '';
  
  let content = `确定要报名参加"${props.examInfo.name}"吗？\n\n`;
  content += `考试时间：${formatDateTime(props.examInfo.examTime)}\n`;
  content += `考试地点：${props.examInfo.location}\n`;
  
  if (props.examInfo.fee > 0) {
    content += `考试费用：¥${props.examInfo.fee}`;
  }
  
  return content;
});

function getDisabledReason() {
  if (!props.examInfo) return '';

  const now = new Date();
  const deadline = new Date(props.examInfo.registrationDeadline);
  const isFull = props.examInfo.registeredCount >= props.examInfo.maxCapacity;

  if (now >= deadline) {
    return '报名已截止';
  } else if (isFull) {
    return '报名人数已满';
  } else if (props.examInfo.status === 'closed') {
    return '报名已关闭';
  } else if (props.examInfo.status === 'cancelled') {
    return '考试已取消';
  }

  return '暂不可报名';
}

function handleShowRegisterModal() {
  showRegisterConfirm.value = true;
}

function handleShowCancelModal() {
  showCancelConfirm.value = true;
}

function handleConfirmRegistration() {
  showRegisterConfirm.value = false;
  emit('register');
}

function handleConfirmCancelRegistration() {
  showCancelConfirm.value = false;
  emit('cancel-registration');
}

function handleDownloadAdmissionTicket() {
  emit('download-admission-ticket');
}

function formatDateTime(dateTimeStr: string) {
  const date = new Date(dateTimeStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.registration-manager {
  padding: 0 $spacing-lg $spacing-lg;

  .registered-info {
    background-color: $success-light;
    border-radius: $border-radius-large;
    padding: $spacing-xl;

    .registered-header {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-lg;

      .registered-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $success-color;
      }
    }

    .registered-details {
      margin-bottom: $spacing-lg;

      .registered-item {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-md;

        .registered-label {
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-right: $spacing-sm;
        }

        .registered-value {
          font-size: $font-size-md;
          color: $text-primary;
          font-weight: $font-weight-medium;
        }
      }
    }

    .registered-actions {
      display: flex;
      gap: $spacing-md;

      .action-btn {
        flex: 1;
        height: 72rpx;
        border: none;
        border-radius: $border-radius-medium;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;

        &.secondary {
          background-color: white;
          color: $success-color;
          border: 2rpx solid $success-color;
        }

        &.danger {
          background-color: $error-color;
          color: white;
        }
      }
    }
  }

  .registration-actions {
    .registration-disabled {
      background-color: $surface-color;
      border-radius: $border-radius-large;
      padding: $spacing-xl;
      text-align: center;

      .disabled-text {
        font-size: $font-size-md;
        color: $text-disabled;
      }
    }

    .register-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, $secondary-color, $secondary-dark);
      color: white;
      border: none;
      border-radius: $border-radius-large;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>