import { ref, onMounted, onUnmounted } from 'vue';
import type { CameraError } from '@/types/api';

/**
 * 摄像头权限管理 Composable
 * 统一管理摄像头权限检查、请求、错误处理等逻辑
 */
export function useCameraAuth() {
  const cameraAuthorized = ref(false);
  const showAuthModal = ref(false);

  /** 检查摄像头权限 */
  async function checkCameraAuth() {
    try {
      const res = await uni.getSetting();
      const authSetting = res.authSetting;
      
      if (authSetting['scope.camera'] === false) {
        // 用户已拒绝授权
        cameraAuthorized.value = false;
      } else if (authSetting['scope.camera'] === true) {
        // 用户已授权
        cameraAuthorized.value = true;
      } else {
        // 未请求过权限，尝试请求
        try {
          await uni.authorize({
            scope: 'scope.camera',
          });
          cameraAuthorized.value = true;
        } catch (error) {
          cameraAuthorized.value = false;
        }
      }
    } catch (error) {
      console.error('获取授权状态失败:', error);
    }
  }

  /** 请求摄像头授权 */
  async function requestCameraAuth() {
    try {
      await uni.authorize({
        scope: 'scope.camera',
      });
      cameraAuthorized.value = true;
    } catch (error) {
      // 用户拒绝授权，显示引导弹窗
      showAuthModal.value = true;
    }
  }

  /** 打开设置页面 */
  function openSetting() {
    uni.openSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          cameraAuthorized.value = true;
        }
      },
    });
  }

  /** 取消授权 */
  function handleAuthCancel() {
    showAuthModal.value = false;
    uni.showToast({
      title: '需要摄像头权限才能进行人脸识别',
      icon: 'none',
      duration: 2000,
    });
  }

  /** 摄像头就绪回调 */
  function onCameraReady() {
    console.log('摄像头准备就绪');
    // 确保摄像头权限状态正确
    cameraAuthorized.value = true;
  }

  /** 摄像头错误处理 */
  function onCameraError(error: CameraError) {
    console.error('摄像头错误:', error);

    // 摄像头错误处理
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny') || error.errMsg.includes('permission')) {
        cameraAuthorized.value = false;
        showAuthModal.value = true;
      } else if (error.errMsg.includes('device not found')) {
        uni.showToast({
          title: '未找到摄像头设备',
          icon: 'none',
          duration: 2000,
        });
      } else if (error.errMsg.includes('system error')) {
        uni.showToast({
          title: '系统错误，请重启应用',
          icon: 'none',
          duration: 2000,
        });
      } else {
        uni.showToast({
          title: error.message || '摄像头初始化失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } else {
      uni.showToast({
        title: '摄像头初始化失败',
        icon: 'none',
        duration: 2000,
      });
    }
  }

  // 组件挂载时检查权限
  onMounted(() => {
    checkCameraAuth();
  });

  // 组件卸载时清理
  onUnmounted(() => {
    // 清理camera相关资源
    cameraAuthorized.value = false;
  });

  return {
    cameraAuthorized,
    showAuthModal,
    checkCameraAuth,
    requestCameraAuth,
    openSetting,
    handleAuthCancel,
    onCameraReady,
    onCameraError,
  };
}