export function useExamUtils() {
  function formatTime(seconds: number) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  }

  function getSubmitModalContent(questionsLength: number, allAnswers: Record<number, string[]>) {
    const unansweredCount = questionsLength - Object.keys(allAnswers).length;
    if (unansweredCount > 0) {
      return `还有${unansweredCount}题未作答，确定要提交吗？`;
    }
    return '确定要提交试卷吗？提交后不可修改。';
  }

  function calculateProgressPercent(currentIndex: number, totalQuestions: number) {
    return totalQuestions > 0 ? ((currentIndex + 1) / totalQuestions) * 100 : 0;
  }

  return {
    formatTime,
    getSubmitModalContent,
    calculateProgressPercent,
  };
}