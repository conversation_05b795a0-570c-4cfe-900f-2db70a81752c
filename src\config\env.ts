/**
 * 环境配置
 * 根据不同环境配置API地址和其他环境相关设置
 */

/** 环境类型 */
export type EnvironmentType = 'development' | 'staging' | 'production';

/** 环境配置接口 */
export interface EnvironmentConfig {
  apiBaseUrl: string;
  enableDebug: boolean;
  version: string;
}

/** 环境配置映射 */
const envConfigs: Record<EnvironmentType, EnvironmentConfig> = {
  development: {
    apiBaseUrl: 'http://127.0.0.1:3000/api/v1',
    enableDebug: true,
    version: 'dev',
  },
  staging: {
    apiBaseUrl: 'http://192.168.31.225:3000/api/v1',
    enableDebug: true,
    version: 'staging',
  },
  production: {
    apiBaseUrl: 'https://api.cdcexam.com/v1',
    enableDebug: false,
    version: '1.0.0',
  },
};

/**
 * 获取当前环境
 * 优先级：manifest配置 > NODE_ENV > 默认development
 */
function getCurrentEnvironment(): EnvironmentType {
  // 尝试从manifest.json读取环境配置
  try {
    // 在uni-app中，可以通过uni.getSystemInfo获取小程序版本信息
    // 这里简化处理，可根据实际需求调整
    const systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'devtools') {
      return 'development';
    }
  } catch (error) {
    console.warn('获取系统信息失败:', error);
  }

  // 从NODE_ENV读取（HBuilderX构建时可能设置）
  if (typeof process !== 'undefined' && process.env) {
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv === 'production') return 'production';
    if (nodeEnv === 'staging') return 'staging';
  }

  // 默认开发环境
  return 'development';
}

/** 当前环境 */
export const currentEnv = getCurrentEnvironment();

/** 当前环境配置 */
export const envConfig = envConfigs[currentEnv];

/** 是否为开发环境 */
export const isDevelopment = currentEnv === 'development';

/** 是否为生产环境 */
export const isProduction = currentEnv === 'production';

/**
 * 获取API基础地址
 */
export function getApiBaseUrl(): string {
  return envConfig.apiBaseUrl;
}

/**
 * 检查是否启用调试模式
 */
export function isDebugEnabled(): boolean {
  return envConfig.enableDebug;
}

/**
 * 获取应用版本
 */
export function getAppVersion(): string {
  return envConfig.version;
}

/**
 * 调试日志输出
 * 只在调试模式下输出
 */
export function debugLog(message: string, ...args: unknown[]): void {
  if (isDebugEnabled()) {
    console.log(`[DEBUG] ${message}`, ...args);
  }
}

/**
 * 环境信息输出
 */
export function logEnvironmentInfo(): void {
  console.log('=== 环境配置信息 ===');
  console.log('当前环境:', currentEnv);
  console.log('API地址:', envConfig.apiBaseUrl);
  console.log('调试模式:', envConfig.enableDebug);
  console.log('应用版本:', envConfig.version);
  console.log('==================');
}