/**
 * 考试中心状态管理
 * 采用Setup Store模式，提供智能缓存和完整的状态管理
 */
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { ExamItem, ExamAttempt, PageData } from '../../types/api';
import { getCurrentExams, getExamHistory } from '../../api/modules/exam';

export const useExamStore = defineStore('exam', () => {
  // State
  const currentExams = ref<ExamItem[]>([]);
  const examHistory = ref<ExamAttempt[]>([]);
  const loading = ref(false);
  const loadingExamId = ref<string | null>(null);
  const lastUpdated = ref(0);
  const historyPage = ref(1);
  const historyHasMore = ref(true);
  const historyLoading = ref(false);

  // Getters
  const shouldRefresh = computed(() => {
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastUpdated.value > fiveMinutes;
  });

  const availableExams = computed(() => 
    currentExams.value.filter(exam => 
      ['not_started', 'in_progress', 'booking_available'].includes(exam.status),
    ),
  );

  const completedExams = computed(() => 
    currentExams.value.filter(exam => 
      ['completed', 'passed', 'failed'].includes(exam.status),
    ),
  );

  // Actions
  async function fetchCurrentExams(forceRefresh = false) {
    if (loading.value) return;
    
    if (!forceRefresh && !shouldRefresh.value && currentExams.value.length > 0) {
      return;
    }

    loading.value = true;
    try {
      const exams = await getCurrentExams();
      currentExams.value = exams;
      lastUpdated.value = Date.now();
    } catch (error) {
      console.error('获取当前考试列表失败:', error);
      uni.showToast({
        title: '获取考试信息失败',
        icon: 'error',
        duration: 2000,
      });
    } finally {
      loading.value = false;
    }
  }

  async function fetchExamHistory(page = 1, pageSize = 10, reset = false) {
    if (historyLoading.value) return;

    if (reset) {
      examHistory.value = [];
      historyPage.value = 1;
      historyHasMore.value = true;
    }

    historyLoading.value = true;
    try {
      const result = await getExamHistory(page, pageSize);
      
      if (reset || page === 1) {
        examHistory.value = result.items;
      } else {
        examHistory.value = [...examHistory.value, ...result.items];
      }
      
      historyPage.value = page;
      historyHasMore.value = result.items.length === pageSize && 
                             examHistory.value.length < result.total;
    } catch (error) {
      console.error('获取考试历史失败:', error);
      uni.showToast({
        title: '获取历史记录失败',
        icon: 'error',
        duration: 2000,
      });
    } finally {
      historyLoading.value = false;
    }
  }

  async function loadMoreHistory() {
    if (!historyHasMore.value || historyLoading.value) return;
    await fetchExamHistory(historyPage.value + 1, 10, false);
  }

  async function refreshExamData() {
    await Promise.all([
      fetchCurrentExams(true),
      fetchExamHistory(1, 10, true),
    ]);
  }

  function setLoadingExam(examId: string | null) {
    loadingExamId.value = examId;
  }

  async function startExam(examId: string) {
    setLoadingExam(examId);
    try {
      // 这里可以添加开始考试的逻辑
      // 比如验证权限、检查考试状态等
      console.log('开始考试:', examId);
      
      // 刷新考试数据以更新状态
      await fetchCurrentExams(true);
    } catch (error) {
      console.error('开始考试失败:', error);
      uni.showToast({
        title: '开始考试失败',
        icon: 'error',
        duration: 2000,
      });
    } finally {
      setLoadingExam(null);
    }
  }

  function clearCache() {
    currentExams.value = [];
    examHistory.value = [];
    lastUpdated.value = 0;
    historyPage.value = 1;
    historyHasMore.value = true;
  }

  return {
    // State
    currentExams,
    examHistory,
    loading,
    loadingExamId,
    lastUpdated,
    historyLoading,
    historyHasMore,
    
    // Getters
    shouldRefresh,
    availableExams,
    completedExams,
    
    // Actions
    fetchCurrentExams,
    fetchExamHistory,
    loadMoreHistory,
    refreshExamData,
    setLoadingExam,
    startExam,
    clearCache,
  };
}); 