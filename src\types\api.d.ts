/**
 * API相关类型定义
 * 更新时间：2025-06-22T00:38:48
 * 修复：与cdcopenapi0620-2.yaml规范完全对齐
 */

/** 通用API响应结构 - 统一包裹结构 */
export interface ApiResponse<T = unknown> {
  code: number;        // 业务状态码，200表示成功
  message: string;     // 响应消息
  data: T;            // 响应数据，泛型类型
}

/** 分页参数 */
export interface PageParams {
  page: number;
  pageSize: number;
}

/** 分页响应数据 */
export interface PageData<T> {
  items: T[];           // 数据列表，与API规范保持一致
  total: number;        // 总数量
  page: number;         // 当前页码
  pageSize: number;     // 每页数量
}

/** 分页响应 - 使用统一包裹结构 */
export type PageResult<T> = ApiResponse<PageData<T>>

// ==================== 用户相关类型 ====================

/** 机构信息 */
export interface Institution {
  id: string;
  name: string;
}

/** 职位信息 */
export interface Position {
  id: string;
  name: string;
}

/** 用户信息 */
export interface UserInfo {
  id: string;
  openid: string;
  unionid?: string;
  nickname: string;
  avatar: string;
  realName?: string;
  phone?: string;
  idCard?: string;
  organization?: string;
  position?: string;
  status: 'new' | 'pending_review' | 'approved' | 'rejected';
  token: string;
  certificateExpiry?: string;
  rejectionReason?: string; // 审核拒绝原因，当status为rejected时返回
}

/** 用户统计数据 */
export interface UserStats {
  totalExams: number;
  passedExams: number;
  failedExams: number;
  totalPracticeTime: number; // 总练习时间（分钟）
  totalQuestions: number;
  correctAnswers: number;
  certificates: number;
  loginDays: number;
}

/** 登录参数 */
export interface LoginParams {
  code: string;
  encryptedData?: string;
  iv?: string;
}

/** 微信登录响应数据 */
export interface WxLoginData {
  token: string;
  userStatus: 'new' | 'pending_review' | 'approved' | 'rejected';
  isVip: boolean;
  vipExpiryDate?: string;
}

/** 微信登录响应 - 使用统一包裹结构 */
export type WxLoginResponse = ApiResponse<WxLoginData>

/** 注册参数 (兼容旧版本) */
export interface RegisterParams {
  realName: string;
  phone: string;
  idCard: string;
  institutionId: string;
  positionId: string;
  avatar: string;
}

/** 个人资料提交请求 (符合API规范) */
export interface ProfileSubmissionRequest {
  name: string;
  phone: string;
  idCardNumber: string;
  photo: File | string; // 文件对象或base64字符串
  institutionId: string;
  positionId: string;
}

// ==================== 信息中心类型 ====================

/** 资讯类型 */
export type ArticleType = 'announcement' | 'policy' | 'notice';

/** 资讯文章 (符合API规范) */
export interface Article {
  id: string;
  title: string;
  content: string;
  type: ArticleType;
  isPinned: boolean;
  createdAt: string;
}

/** 信息项 (兼容旧版本) */
export interface InfoItem {
  id: string;
  title: string;
  content: string;
  summary?: string;
  type: ArticleType;
  isTop: boolean;
  publishTime: string;
  source?: string;
}

/** 信息列表参数 (兼容旧版本) */
export interface InfoListParams extends PageParams {
  type?: string;
}

// ==================== 学习中心类型 ====================

/** 题目类型 (符合API规范) */
export type QuestionType = 'single_choice' | 'multiple_choice' | 'judgment' | 'essay';

/** 题库分类 (符合API规范) */
export interface QuestionBankCategory {
  id: string;
  name: string;
  totalQuestions: number;
}

/** 题目选项 */
export interface QuestionOption {
  key: string;
  value: string;
}

/** 显示用题目 (不含答案) */
export interface QuestionForDisplay {
  id: string;
  type: QuestionType;
  stem: string;
  options?: QuestionOption[];
  score?: number;     // 题目分值
  image?: string;     // 题目配图URL
}

/** 带解析的题目 (含答案) */
export interface QuestionWithSolution {
  id: string;
  type: QuestionType;
  stem: string;
  options?: QuestionOption[];
  correctAnswer: string[];
  explanation?: string;
}

/** 练习会话请求 */
export interface PracticeSessionRequest {
  categoryId: string;
  count: number;
}

/** 练习会话结果 */
export interface PracticeSessionResult {
  sessionId: string;
  questions: QuestionWithSolution[];
}

/** 练习答案提交请求 */
export interface PracticeAnswerRequest {
  questionId: string;
  answers: string[];
}

/** 练习答案提交结果 */
export interface PracticeAnswerResult {
  isCorrect: boolean;
  score: number;
}

/** 题库分类 (兼容旧版本) */
export interface QuestionCategory {
  id: string;
  name: string;
  description?: string;
  questionCount: number;
}

/** 题目 (兼容旧版本) */
export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  options?: QuestionOption[];
  correctAnswer: string | string[];
  analysis?: string;
  categoryId: string;
}

/** 练习结果 (兼容旧版本) */
export interface PracticeResult {
  categoryId: string;
  questions: Array<{
    questionId: string;
    userAnswer: string | string[];
    isCorrect: boolean;
  }>;
  score: number;
  totalTime: number;
}

/** 练习统计 */
export interface PracticeStats {
  totalSessions: number;
  averageScore: number;
  totalQuestions: number;
  correctAnswers: number;
  categoryStats: Array<{
    categoryId: string;
    categoryName: string;
    sessionsCount: number;
    averageScore: number;
  }>;
}

// ==================== 考试中心类型 ====================

/** 考试类型 (符合API规范) */
export type ExamType = 'online' | 'offline';

/** 考试状态 */
export type ExamStatus = 'not_started' | 'in_progress' | 'completed' | 'expired' | 'passed' | 'failed';

/** 用户考试状态 (符合OpenAPI规范) */
export type ExamUserStatus = 
  | 'not_started'        // 未开始
  | 'booking_available'  // 可预约
  | 'booked'            // 已预约
  | 'ready_to_start'    // 准备开始
  | 'in_progress'       // 进行中
  | 'passed'            // 已通过
  | 'failed'            // 未通过
  | 'failed_final'      // 最终未通过
  | 'finished'          // 已结束
  | 'pending_grading';  // 待阅卷

/** 考试历史记录 */
export interface ExamAttempt {
  id: string;
  examId: string;
  examName: string;
  type: ExamType;
  startTime: string;
  completedTime?: string;
  score?: number;
  status: ExamUserStatus;
  isLatest: boolean;
}

/** 考试 (符合API规范) */
export interface Exam {
  id: string;
  name: string;
  type: ExamType;
  status: ExamUserStatus;
  startTime: string;
  endTime: string;
  canRetry: boolean;
  description?: string;
  rules?: string[];
  remainingTime?: number;
}

/** 考试结果摘要 */
export interface ExamAttemptResult {
  score: number | null;    // 最终得分，pending_grading状态时为null
  completedAt: string;     // 完成考试的时间
}

/** 线下考试报名详情 */
export interface ExamBookingDetails {
  bookingId: string;
  venueName: string;           // 报名的考点名称
  scheduleStartTime: string;   // 报名的场次开始时间
}

/** 考试项 - 统一与API规范对齐 */
export interface ExamItem {
  id: string;
  name: string;
  type: ExamType;
  status: ExamUserStatus;  // 更新为使用 ExamUserStatus
  startTime: string;
  endTime: string;
  canRetry: boolean;       // 新增：是否允许重考
  description?: string;    // 新增：考试描述
  duration?: number;       // 考试时长(分钟) - 改为可选
  totalQuestions?: number; // 题目总数 - 改为可选
  passScore?: number;      // 及格分数 - 改为可选
  currentScore?: number;   // 当前分数
  remainingAttempts?: number; // 剩余尝试次数
  attemptResult?: ExamAttemptResult | null; // 考试结果摘要
  bookingDetails?: ExamBookingDetails | null; // 线下考试报名详情
}

/** 考试详情 */
export interface ExamDetail extends ExamItem {
  rules?: string[];       // 考试规则 - 改为可选，因为基类已包含
  venues?: ExamVenue[];   // 线下考试考场信息
}

/** 考场信息 */
export interface ExamVenue {
  id: string;
  name: string;
  address: string;
  schedules: ExamSchedule[];
}

/** 线下考场详细信息 (含联系信息) */
export interface OfflineVenueSchedule {
  venueId: string;
  venueName: string;
  schedules: Array<{
    scheduleId: string;
    startTime: string;
    endTime: string;
    totalSlots: number;
    availableSlots: number;
    venueAddress: string;     // 考场详细地址
    contactPerson: string;    // 考场联系人
    contactPhone: string;     // 考场联系电话
    notes?: string;           // 考场备注信息
  }>;
}

/** 考试场次 */
export interface ExamSchedule {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  capacity: number;
  registered: number;
  isBooked: boolean;
}

/** 线下考试预约 */
export interface OfflineBooking {
  bookingId: string;
  examName: string;
  venueName: string;
  startTime: string;
}

/** 考试答案 */
export interface ExamAnswer {
  questionId: string;
  answer: string | string[];
  timeSpent: number;
}

/** 人脸识别结果 */
export interface FaceVerifyResult {
  success: boolean;
  confidence: number;
  message: string;
}

// ==================== 证书相关类型 ====================

/** 证书状态 (符合API规范) */
export type CertificateStatus = 'pending_approval' | 'valid' | 'expired' | 'revoked';

/** 证书类型 */
export type CertificateType = 'qualification' | 'training' | 'skill' | 'achievement';

/** 证书信息 (符合API规范) */
export interface Certificate {
  id: string;
  name: string;
  status: CertificateStatus;
  imageUrl?: string;
  issueDate?: string;
  expiryDate?: string;
}

// ==================== 用户状态枚举 ====================

/** 用户状态 (符合API规范) */
export type UserStatus = 'new' | 'pending_review' | 'approved' | 'rejected';

// ==================== 错误响应类型 ====================

/** 错误响应 */
export interface ErrorResponse {
  code: number;
  message: string;
}

// ==================== 摄像头相关类型 ====================

/** 摄像头错误信息 */
export interface CameraError {
  errMsg?: string;
  message?: string;
  code?: number;
  type?: 'permission' | 'hardware' | 'network' | 'unknown';
}

/** 滚动事件详情 */
export interface ScrollEvent {
  detail: {
    scrollTop: number;
    scrollLeft?: number;
    scrollHeight?: number;
    scrollWidth?: number;
  };
}

// ==================== HTTP扩展类型 ====================

/** 文件上传对象 */
export interface UploadFileObject {
  tempFilePath: string;
  type?: string;
  name?: string;
  size?: number;
}

/** 扩展的HTTP实例接口 */
export interface ExtendedHttpInstance {
  upload: (url: string, options: { 
    data: Record<string, unknown>;
    onProgress?: (progress: number) => void;
  }) => Promise<unknown>;
}
