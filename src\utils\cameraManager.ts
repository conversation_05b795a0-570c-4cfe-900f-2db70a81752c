/**
 * 摄像头管理工具
 * 用于确保摄像头资源的正确管理和避免多实例冲突
 */

let cameraInstance: UniApp.CameraContext | null = null;
let cameraInitialized = false;
let activeCameraId: string | null = null;

/**
 * 初始化摄像头
 */
export function initCamera(): Promise<boolean> {
  return new Promise((resolve) => {
    if (cameraInitialized) {
      console.log('摄像头已初始化');
      resolve(true);
      return;
    }

    try {
      // 检查摄像头权限
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.camera'] === true) {
            cameraInitialized = true;
            console.log('摄像头初始化成功');
            resolve(true);
          } else {
            console.log('摄像头权限未授权');
            resolve(false);
          }
        },
        fail: () => {
          console.error('获取摄像头权限状态失败');
          resolve(false);
        },
      });
    } catch (error) {
      console.error('摄像头初始化失败:', error);
      resolve(false);
    }
  });
}

/**
 * 清理摄像头资源
 */
export function cleanupCamera(): void {
  try {
    if (cameraInstance) {
      console.log('清理摄像头实例:', activeCameraId);
      cameraInstance = null;
    }
    cameraInitialized = false;
    activeCameraId = null;
    console.log('摄像头资源清理完成');
  } catch (error) {
    console.error('清理摄像头资源失败:', error);
  }
}

/**
 * 获取摄像头上下文
 */
export function getCameraContext(cameraId?: string): UniApp.CameraContext | null {
  try {
    const targetId = cameraId || 'default';

    // 如果已有不同ID的摄像头实例，先清理
    if (cameraInstance && activeCameraId && activeCameraId !== targetId) {
      console.log('检测到不同的摄像头ID，清理旧实例:', activeCameraId);
      cleanupCamera();
    }

    if (!cameraInstance) {
      cameraInstance = uni.createCameraContext(cameraId);
      activeCameraId = targetId;
      console.log('创建摄像头上下文:', targetId);
    }
    return cameraInstance;
  } catch (error) {
    console.error('获取摄像头上下文失败:', error);
    return null;
  }
}

/**
 * 检查摄像头是否可用
 */
export function isCameraAvailable(): boolean {
  return cameraInitialized;
}

/**
 * 重置摄像头状态
 */
export function resetCamera(): void {
  cleanupCamera();
  console.log('摄像头状态已重置');
}
