/**
 * 验证工具函数
 */

/**
 * 验证手机号
 * @param phone 手机号
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 */
export function validateIdCard(idCard: string): boolean {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
}

/**
 * 验证邮箱
 * @param email 邮箱
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证密码强度
 * @param password 密码
 * @param minLength 最小长度，默认8位
 */
export function validatePassword(password: string, minLength = 8): {
  isValid: boolean;
  strength: 'weak' | 'medium' | 'strong';
  message: string;
} {
  if (password.length < minLength) {
    return {
      isValid: false,
      strength: 'weak',
      message: `密码长度不能少于${minLength}位`,
    };
  }
  
  let score = 0;
  
  // 包含小写字母
  if (/[a-z]/.test(password)) score++;
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) score++;
  
  // 包含数字
  if (/\d/.test(password)) score++;
  
  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
  
  if (score < 2) {
    return {
      isValid: false,
      strength: 'weak',
      message: '密码强度太弱，请包含字母、数字或特殊字符',
    };
  } else if (score < 3) {
    return {
      isValid: true,
      strength: 'medium',
      message: '密码强度中等',
    };
  } else {
    return {
      isValid: true,
      strength: 'strong',
      message: '密码强度很强',
    };
  }
}

/**
 * 验证中文姓名
 * @param name 姓名
 */
export function validateChineseName(name: string): boolean {
  const nameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
  return nameRegex.test(name);
}

/**
 * 验证URL
 * @param url URL地址
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证图片文件类型
 * @param fileName 文件名
 */
export function validateImageFile(fileName: string): boolean {
  const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp)$/i;
  return imageExtensions.test(fileName);
}

/**
 * 验证文件大小
 * @param fileSize 文件大小（字节）
 * @param maxSize 最大大小（字节），默认2MB
 */
export function validateFileSize(fileSize: number, maxSize = 2 * 1024 * 1024): boolean {
  return fileSize <= maxSize;
}

/**
 * 验证表单字段
 * @param value 字段值
 * @param rules 验证规则
 */
export function validateField(value: unknown, rules: ValidationRule[]): ValidationResult {
  for (const rule of rules) {
    const result = rule.validator(value);
    if (!result.isValid) {
      return result;
    }
  }
  
  return { isValid: true, message: '' };
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  validator: (value: unknown) => ValidationResult;
  message?: string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * 常用验证规则
 */
export const commonRules = {
  required: (message = '此字段为必填项'): ValidationRule => ({
    validator: (value: unknown) => ({
      isValid: value !== null && value !== undefined && value !== '',
      message,
    }),
  }),
  
  minLength: (min: number, message?: string): ValidationRule => ({
    validator: (value: unknown) => {
      const str = String(value || '');
      return {
        isValid: str.length >= min,
        message: message || `最少需要${min}个字符`,
      };
    },
  }),
  
  maxLength: (max: number, message?: string): ValidationRule => ({
    validator: (value: unknown) => {
      const str = String(value || '');
      return {
        isValid: str.length <= max,
        message: message || `最多允许${max}个字符`,
      };
    },
  }),
  
  pattern: (regex: RegExp, message = '格式不正确'): ValidationRule => ({
    validator: (value: unknown) => ({
      isValid: regex.test(String(value || '')),
      message,
    }),
  }),
};
