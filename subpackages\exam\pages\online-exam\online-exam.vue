<template>
  <view class="online-exam-container">
    <!-- 考前准备阶段 -->
    <exam-preparation
      v-if="examStage === 'preparation'"
      :exam-info="examInfo"
      :exam-rules-data="examRulesData"
      @start-exam="handleStartExam"
    />

    <!-- 人脸识别阶段 -->
    <face-verification
      v-else-if="examStage === 'faceVerification'"
      :key="'face-verification-' + Date.now()"
      :exam-id="props.id"
      @camera-ready="onCameraReady"
      @camera-error="onCameraError"
      @face-verified="handleFaceVerified"
      @exit-exam="handleExitExam"
    />

    <!-- 考试进行阶段 -->
    <exam-execution
      v-else-if="examStage === 'examination'"
      :attempt-id="attemptId"
      :questions="examQuestions"
      :current-question-index="currentQuestionIndex"
      :selected-answers="selectedAnswers"
      :all-answers="allAnswers"
      :remaining-time="remainingTime"
      @select-option="handleSelectOption"
      @prev-question="handlePrevQuestion"
      @next-question="handleNextQuestion"
      @submit-exam="handleSubmitExam"
      @essay-input="handleEssayInput"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { submitOnlineExamAnswers, getOnlineExamRules } from '../../../../src/api/modules/exam';
import { forceCleanupCamera } from '../../../../src/utils/globalCameraState';
import type { QuestionForDisplay } from '../../../../src/types/api';
import ExamPreparation from '../../../../src/components/business/ExamPreparation.vue';
import FaceVerification from '../../../../src/components/business/FaceVerification.vue';
import ExamExecution from '../../../../src/components/business/ExamExecution.vue';

const props = defineProps<{
  id: string;
}>();

const examStage = ref<'preparation' | 'faceVerification' | 'examination' | 'completed'>('preparation');
const examInfo = ref<{
  id: string;
  name: string;
  duration: number;
  totalQuestions: number;
  passScore: number;
}>({
  id: props.id,
  name: '在线考试',
  duration: 120, // 默认120分钟，会被API返回的数据覆盖
  totalQuestions: 0,
  passScore: 60,
});
const examRulesData = ref<{
  rules?: string[];
  duration?: number;
  totalQuestions?: number;
  passScore?: number;
  countdownTime?: number;
} | null>(null);
const examQuestions = ref<QuestionForDisplay[]>([]);
const attemptId = ref<string>('');
const currentQuestionIndex = ref(0);
const selectedAnswers = ref<string[]>([]);
const allAnswers = ref<Record<number, string[]>>({});
const remainingTime = ref(0);

let examTimer: NodeJS.Timeout | null = null;
let countdownTimer: NodeJS.Timeout | null = null;

onMounted(() => {
  loadExamRules();
});

onUnmounted(() => {
  if (examTimer) {
    clearInterval(examTimer);
  }
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

// 监听考试阶段变化，动态更新导航标题
watch(examStage, (newStage) => {
  const titleMap: Record<string, string> = {
    preparation: '考前准备',
    faceVerification: '人脸识别', 
    examination: '在线答题',
    completed: '考试完成'
  };
  
  const title = titleMap[newStage] || '资格考试';
  uni.setNavigationBarTitle({
    title: title
  });
}, { immediate: true });

async function loadExamRules() {
  try {
    const response = await getOnlineExamRules(props.id);
    // 构造符合ExamRulesData接口的数据结构
    examRulesData.value = {
      rules: response.rules,
      duration: response.durationInMinutes,
      totalQuestions: response.totalQuestions,
      passScore: response.passingScore,
      countdownTime: response.countdownSeconds || 5,
    };
    
    // 更新examInfo的信息
    if (response.durationInMinutes) {
      examInfo.value.duration = response.durationInMinutes;
      remainingTime.value = response.durationInMinutes * 60;
    }
    if (response.totalQuestions) {
      examInfo.value.totalQuestions = response.totalQuestions;
    }
    if (response.passingScore) {
      examInfo.value.passScore = response.passingScore;
    }
    
    startCountdown();
  } catch (error) {
    console.error('加载考前须知失败:', error);
    uni.showToast({
      title: '加载考前须知失败',
      icon: 'none',
    });
  }
}

function startCountdown() {
  if (!examRulesData.value?.countdownTime) return;
  
  countdownTimer = setInterval(() => {
    if (examRulesData.value.countdownTime > 0) {
      examRulesData.value.countdownTime--;
    } else {
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
    }
  }, 1000);
}

function handleStartExam() {
  // 先隐藏当前组件
  examStage.value = '';

  // 强制清理摄像头状态
  forceCleanupCamera();

  // 延迟后再显示人脸识别组件，确保之前的组件完全销毁
  // 增加延迟时间以确保摄像头资源被完全释放
  setTimeout(() => {
    examStage.value = 'faceVerification';
  }, 800);
}

function onCameraReady() {
  console.log('摄像头准备就绪');
}

function onCameraError(error: unknown) {
  console.error('摄像头错误:', error);
  uni.showToast({
    title: '摄像头启动失败',
    icon: 'none',
  });
}

function handleFaceVerified(data: { attemptId: string; questions: QuestionForDisplay[] }) {
  try {
    // 验证返回的数据完整性
    if (!data || !data.attemptId || !data.questions) {
      throw new Error('人脸验证返回数据不完整');
    }
    
    if (!Array.isArray(data.questions) || data.questions.length === 0) {
      throw new Error('考试题目数据为空');
    }
    
    // 验证题目数据结构
    const invalidQuestions = data.questions.filter(q => !q.id || !q.type || !q.stem);
    if (invalidQuestions.length > 0) {
      console.warn('检测到无效题目数据:', invalidQuestions);
    }
    
    // 接收人脸验证成功后返回的数据
    attemptId.value = data.attemptId;
    examQuestions.value = data.questions;
    
    console.log('考试数据加载成功:', {
      attemptId: data.attemptId,
      questionsCount: data.questions.length,
      firstQuestion: data.questions[0],
    });
    
    startExamination();
  } catch (error) {
    console.error('处理考试数据失败:', error);
    uni.showModal({
      title: '数据加载失败',
      content: error instanceof Error ? error.message : '考试数据加载失败，请重新开始考试',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      },
    });
  }
}

function handleExitExam() {
  uni.navigateBack();
}

async function startExamination() {
  try {
    // 先隐藏人脸识别组件
    examStage.value = '';
    
    // 题目数据已从人脸验证阶段获取，直接开始考试
    setTimeout(() => {
      examStage.value = 'examination';
      startExamTimer();
    }, 100);
    
    uni.onAppHide(() => {
      handleAppHide();
    });
  } catch (error) {
    console.error('开始考试失败:', error);
    uni.showToast({
      title: '开始考试失败',
      icon: 'none',
    });
  }
}

function startExamTimer() {
  examTimer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      handleSubmitExam();
    }
  }, 1000);
}

function handleAppHide() {
  uni.showModal({
    title: '考试异常',
    content: '检测到您切换了应用，这可能影响考试成绩。请保持在考试界面。',
    showCancel: false,
  });
}

function handleSelectOption(optionKey: string) {
  const question = examQuestions.value[currentQuestionIndex.value];
  
  if (!question) {
    console.warn('当前题目不存在');
    return;
  }

  console.log('处理答案选择:', { 
    questionType: question.type, 
    optionKey, 
    currentAnswers: selectedAnswers.value, 
  });

  if (question.type === 'single_choice' || question.type === 'single') {
    selectedAnswers.value = [optionKey];
  } else if (question.type === 'multiple_choice' || question.type === 'multiple') {
    const index = selectedAnswers.value.indexOf(optionKey);
    if (index > -1) {
      selectedAnswers.value.splice(index, 1);
    } else {
      selectedAnswers.value.push(optionKey);
    }
  } else if (question.type === 'judgment' || question.type === 'judge') {
    selectedAnswers.value = [optionKey];
  }
  
  console.log('答案选择后:', selectedAnswers.value);
}

function handleEssayInput(value: string) {
  // 对于问答题，将答案存储为单项数组
  selectedAnswers.value = [value];
  console.log('问答题输入:', value);
}

function handlePrevQuestion() {
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  currentQuestionIndex.value--;
  loadQuestionAnswers();
}

function handleNextQuestion() {
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  currentQuestionIndex.value++;
  loadQuestionAnswers();
}

function loadQuestionAnswers() {
  const savedAnswers = allAnswers.value[currentQuestionIndex.value];
  selectedAnswers.value = savedAnswers ? [...savedAnswers] : [];
}

async function handleSubmitExam() {
  console.log('online-exam: 开始处理考试提交');
  try {
    // 保存当前题目答案
    if (selectedAnswers.value.length > 0) {
      allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
    }

    // 清除考试计时器
    if (examTimer) {
      clearInterval(examTimer);
    }

    // 验证答案数据
    const unansweredQuestions = examQuestions.value.filter((_, index) => !allAnswers.value[index]);
    if (unansweredQuestions.length > 0) {
      const result = await uni.showModal({
        title: '提示',
        content: `您还有 ${unansweredQuestions.length} 道题未作答，确定要提交吗？`,
        confirmText: '确认提交',
        cancelText: '继续答题',
      });
      
      if (!result.confirm) {
        // 用户选择继续答题，重新开始计时
        startExamTimer();
        return;
      }
    }

    uni.showLoading({ title: '提交中...' });

    // 构造答案数据，确保格式符合API要求
    const answers = Object.entries(allAnswers.value).map(([questionIndex, answerKeys]) => {
      const questionId = examQuestions.value[Number(questionIndex)]?.id;
      if (!questionId) {
        console.warn(`题目 ${questionIndex} 的ID不存在`);
        return null;
      }
      return {
        questionId,
        answer: answerKeys || [],
      };
    }).filter(Boolean); // 过滤掉null值

    console.log('提交答案数据:', { attemptId: attemptId.value, answers });

    // 调用API提交答案
    try {
      const response = await submitOnlineExamAnswers(attemptId.value, answers);
      
      uni.hideLoading();

      // 处理提交成功 - API成功调用即表示提交成功
      // 注意：response可能为null，这是正常的成功响应
      console.log('提交响应:', response);
      
      examStage.value = 'completed';
      
      uni.showToast({
        title: '考试提交成功',
        icon: 'success',
        duration: 2000,
      });

      // 延迟跳转到历史记录页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/subpackages/exam/pages/history/history`,
        });
      }, 2000);
      
      // 提交成功，直接返回避免进入catch块
      return;
      
    } catch (submitError) {
      uni.hideLoading();
      console.error('API调用失败:', submitError);
      
      // 如果是API调用失败，抛出具体错误
      throw new Error(submitError?.message || '提交失败，请检查网络连接');
    }

  } catch (error) {
    uni.hideLoading();
    console.error('提交考试失败:', error);
    
    // 分析错误类型并提供具体的用户提示
    let errorTitle = '提交失败';
    let errorMessage = '提交失败，请重试';
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // 网络相关错误
      if (error.message.includes('网络') || error.message.includes('连接')) {
        errorTitle = '网络错误';
        errorMessage = '网络连接异常，请检查网络后重试';
      }
      // 服务器错误
      else if (error.message.includes('服务器') || error.message.includes('500')) {
        errorTitle = '服务器异常';
        errorMessage = '服务器暂时无法处理请求，请稍后重试';
      }
      // 权限错误
      else if (error.message.includes('401') || error.message.includes('权限')) {
        errorTitle = '身份验证失败';
        errorMessage = '登录已过期，请重新登录后重试';
      }
    }
    
    // 记录详细错误信息用于调试
    console.log('错误详情:', {
      type: error.constructor.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
    
    uni.showModal({
      title: errorTitle,
      content: errorMessage,
      showCancel: true,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          console.log('用户选择重试提交考试');
          handleSubmitExam();
        } else {
          // 用户取消，重新开始计时
          console.log('用户取消重试，重新开始计时');
          startExamTimer();
        }
      },
    });
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.online-exam-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8faff 0%, #f0f5ff 100%);
  position: relative;
}
</style>