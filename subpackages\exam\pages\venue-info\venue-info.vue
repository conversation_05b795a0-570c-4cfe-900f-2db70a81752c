<template>
  <view class="venue-info-page">
    <!-- 页面标题 -->
    <u-navbar 
      title="考场信息"
      :auto-back="true"
      background="#f8f9fa"
      :border="false"
    />
    
    <!-- 加载状态 -->
    <view
      v-if="loading"
      class="loading-container"
    >
      <u-loading-page 
        :loading="true"
        loading-text="加载考场信息中..."
        :bg-color="'transparent'"
        loading-color="#4CAF50"
      />
    </view>
    
    <!-- 考场信息内容 -->
    <view
      v-else-if="venueInfo"
      class="venue-content"
    >
      <!-- 考试基本信息 -->
      <view class="exam-info-card">
        <view class="card-header">
          <view class="exam-icon">
            📋
          </view>
          <view class="exam-details">
            <u-text 
              :text="examName"
              :bold="true"
              size="18"
              color="#303133"
              :lines="2"
            />
            <u-text 
              text="线下考试"
              size="14"
              color="#909399"
              :margin="'8rpx 0 0 0'"
            />
          </view>
        </view>
      </view>
      
      <!-- 考场详情列表 -->
      <view class="venue-list">
        <view 
          v-for="venue in venueInfo" 
          :key="venue.venueId"
          class="venue-card"
        >
          <view class="venue-header">
            <view class="venue-icon">
              🏢
            </view>
            <u-text 
              :text="venue.venueName"
              :bold="true"
              size="16"
              color="#303133"
            />
          </view>
          
          <!-- 场次信息 -->
          <view class="schedule-list">
            <view 
              v-for="schedule in venue.schedules"
              :key="schedule.scheduleId"
              class="schedule-item"
            >
              <!-- 时间信息 -->
              <view class="schedule-header">
                <view class="time-info">
                  <u-icon
                    name="clock"
                    size="16"
                    color="#4CAF50"
                  />
                  <u-text 
                    :text="formatScheduleTime(schedule.startTime, schedule.endTime)"
                    size="15"
                    color="#303133"
                    :bold="true"
                  />
                </view>
                <view class="slot-info">
                  <u-tag 
                    :text="`${schedule.availableSlots}/${schedule.totalSlots}`"
                    type="info"
                    size="mini"
                    shape="circle"
                  />
                </view>
              </view>
              
              <!-- 详细信息 -->
              <view class="schedule-details">
                <!-- 地址 -->
                <view class="detail-item">
                  <view class="detail-icon">
                    <u-icon
                      name="location"
                      size="14"
                      color="#909399"
                    />
                  </view>
                  <view class="detail-content">
                    <u-text 
                      text="考场地址"
                      size="13"
                      color="#909399"
                      :margin="'0 0 4rpx 0'"
                    />
                    <u-text 
                      :text="schedule.venueAddress"
                      size="14"
                      color="#303133"
                    />
                  </view>
                </view>
                
                <!-- 联系人 -->
                <view class="detail-item">
                  <view class="detail-icon">
                    <u-icon
                      name="account"
                      size="14"
                      color="#909399"
                    />
                  </view>
                  <view class="detail-content">
                    <u-text 
                      text="联系人"
                      size="13"
                      color="#909399"
                      :margin="'0 0 4rpx 0'"
                    />
                    <u-text 
                      :text="schedule.contactPerson"
                      size="14"
                      color="#303133"
                    />
                  </view>
                </view>
                
                <!-- 联系电话 -->
                <view class="detail-item">
                  <view class="detail-icon">
                    <u-icon
                      name="phone"
                      size="14"
                      color="#909399"
                    />
                  </view>
                  <view class="detail-content">
                    <u-text 
                      text="联系电话"
                      size="13"
                      color="#909399"
                      :margin="'0 0 4rpx 0'"
                    />
                    <view class="phone-row">
                      <u-text 
                        :text="schedule.contactPhone"
                        size="14"
                        color="#303133"
                      />
                      <u-button
                        text="拨打"
                        type="primary"
                        size="mini"
                        shape="circle"
                        :custom-style="callButtonStyle"
                        @click="callPhone(schedule.contactPhone)"
                      />
                    </view>
                  </view>
                </view>
                
                <!-- 备注 -->
                <view
                  v-if="schedule.notes"
                  class="detail-item"
                >
                  <view class="detail-icon">
                    <u-icon
                      name="info-circle"
                      size="14"
                      color="#909399"
                    />
                  </view>
                  <view class="detail-content">
                    <u-text 
                      text="备注信息"
                      size="13"
                      color="#909399"
                      :margin="'0 0 4rpx 0'"
                    />
                    <u-text 
                      :text="schedule.notes"
                      size="14"
                      color="#606266"
                      :lines="3"
                    />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view
      v-else
      class="error-state"
    >
      <view class="error-icon">
        <u-icon
          name="error-circle"
          size="64"
          color="#f56c6c"
        />
      </view>
      <u-text 
        text="加载考场信息失败"
        size="16"
        color="#909399"
        align="center"
        :margin="'16rpx 0 8rpx 0'"
      />
      <u-button
        text="重新加载"
        type="primary"
        size="normal"
        shape="circle"
        :custom-style="retryButtonStyle"
        @click="loadVenueInfo"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { OfflineVenueSchedule } from '../../../../src/types/api';
import { formatExamTime } from '../../../../src/utils/format';

// 页面参数
const props = defineProps<{
  examId?: string
}>();

// 响应式数据
const loading = ref<boolean>(true);
const venueInfo = ref<OfflineVenueSchedule[] | null>(null);
const examName = ref<string>('');

// 计算属性
const callButtonStyle = computed(() => ({
  width: '120rpx',
  height: '48rpx',
  fontSize: '24rpx',
}));

const retryButtonStyle = computed(() => ({
  width: '240rpx',
  background: 'linear-gradient(135deg, #4CAF50, #2196F3)',
  boxShadow: '0 8rpx 24rpx rgba(76, 175, 80, 0.3)',
}));

// 生命周期
onMounted(async () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1] as any;
  const examId = currentPage.options?.examId || props.examId;
  
  if (examId) {
    await loadVenueInfo(examId);
  } else {
    uni.showToast({
      title: '缺少考试ID',
      icon: 'error',
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 方法
async function loadVenueInfo(examId?: string): Promise<void> {
  if (!examId) {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1] as any;
    examId = currentPage.options?.examId;
  }
  
  if (!examId) return;
  
  loading.value = true;
  
  try {
    // 模拟API调用 - 实际应该调用API
    // const response = await getOfflineExamSchedules(examId)
    
    // 模拟数据
    setTimeout(() => {
      venueInfo.value = [{
        venueId: 'venue-001',
        venueName: '市疾控中心一号考场',
        schedules: [{
          scheduleId: 'schedule-001',
          startTime: '2025-07-10T09:00:00Z',
          endTime: '2025-07-10T11:00:00Z',
          totalSlots: 50,
          availableSlots: 12,
          venueAddress: '深圳市南山区科技路1号疾控中心大楼3楼301室',
          contactPerson: '李老师',
          contactPhone: '0755-12345678',
          notes: '请提前30分钟到场，携带身份证件。考试期间请保持手机静音。',
        }],
      }];
      
      examName.value = '2025年操作技能线下考核';
      loading.value = false;
    }, 1000);
    
  } catch (error) {
    console.error('加载考场信息失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'error',
    });
    venueInfo.value = null;
    loading.value = false;
  }
}

function formatScheduleTime(startTime: string, endTime: string): string {
  const start = new Date(startTime);
  const end = new Date(endTime);
  
  const formatTime = (date: Date): string => {
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${month}/${day} ${hours}:${minutes}`;
  };
  
  return `${formatTime(start)} - ${formatTime(end)}`;
}

function callPhone(phoneNumber: string): void {
  uni.makePhoneCall({
    phoneNumber,
    success: () => {
      console.log('拨打电话成功');
    },
    fail: (error) => {
      console.error('拨打电话失败:', error);
      uni.showToast({
        title: '拨打失败',
        icon: 'error',
      });
    },
  });
}
</script>

<style lang="scss" scoped>
.venue-info-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.loading-container {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.venue-content {
  padding: 32rpx;
}

.exam-info-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }
  
  .exam-icon {
    font-size: 48rpx;
    line-height: 1;
  }
  
  .exam-details {
    flex: 1;
  }
}

.venue-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.venue-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.venue-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  
  .venue-icon {
    font-size: 32rpx;
    line-height: 1;
  }
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.schedule-item {
  border: 2rpx solid rgba(76, 175, 80, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  background: rgba(76, 175, 80, 0.02);
}

.schedule-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  
  .time-info {
    display: flex;
    align-items: center;
    gap: 12rpx;
  }
}

.schedule-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  
  .detail-icon {
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 4rpx;
  }
  
  .detail-content {
    flex: 1;
  }
  
  .phone-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 60vh;
  
  .error-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.1), rgba(245, 108, 108, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24rpx;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .venue-content {
    padding: 24rpx;
  }
  
  .exam-info-card,
  .venue-card {
    padding: 24rpx;
  }
  
  .schedule-item {
    padding: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .venue-info-page {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
  }
  
  .exam-info-card,
  .venue-card {
    background: rgba(45, 55, 72, 0.9);
  }
  
  .schedule-item {
    background: rgba(76, 175, 80, 0.1);
    border-color: rgba(76, 175, 80, 0.2);
  }
}
</style> 