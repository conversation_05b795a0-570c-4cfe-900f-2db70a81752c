<template>
  <view class="announcement-list-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">
        公告列表
      </text>
      <text class="page-desc">
        查看最新公告信息
      </text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <u-search
        v-model="searchKeyword"
        placeholder="搜索公告标题"
        :show-action="false"
        bg-color="#f5f5f5"
        @search="handleSearch"
        @clear="handleClear"
      />
    </view>

    <!-- 公告列表 -->
    <view class="announcement-list">
      <view
        v-if="loading"
        class="loading-state"
      >
        <u-loading-icon mode="spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>

      <view
        v-else-if="announcementList.length === 0"
        class="empty-state"
      >
        <text class="empty-icon">
          📢
        </text>
        <text class="empty-text">
          暂无公告信息
        </text>
      </view>

      <view v-else>
        <view 
          v-for="item in announcementList" 
          :key="item.id"
          class="announcement-item"
          @tap="goToDetail(item)"
        >
          <view class="item-header">
            <text class="item-title">
              {{ item.title }}
            </text>
            <view
              v-if="item.isTop"
              class="top-badge"
            >
              置顶
            </view>
          </view>
          
          <text class="item-summary">
            {{ item.summary }}
          </text>
          
          <view class="item-footer">
            <text class="item-time">
              {{ formatTime(item.publishTime) }}
            </text>
            <text class="item-views">
              阅读 {{ item.viewCount || 0 }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view
      v-if="hasMore && !loading"
      class="load-more"
      @tap="loadMore"
    >
      <text class="load-more-text">
        点击加载更多
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAnnouncementList } from '../../../../src/api/modules/info';
import type { InfoItem } from '../../../../src/types/api';

// 响应式数据
const announcementList = ref<InfoItem[]>([]);
const loading = ref(false);
const searchKeyword = ref('');
const currentPage = ref(1);
const hasMore = ref(true);
const pageSize = 10;

onMounted(() => {
  loadAnnouncementList();
});

/**
 * 加载公告列表
 */
async function loadAnnouncementList(isLoadMore = false) {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const params = {
      page: isLoadMore ? currentPage.value : 1,
      pageSize,
      keyword: searchKeyword.value,
    };
    
    const result = await getAnnouncementList(params);
    
    if (isLoadMore) {
      announcementList.value.push(...result);
    } else {
      announcementList.value = result;
      currentPage.value = 1;
    }
    
    hasMore.value = result.length === pageSize;
    
  } catch (error) {
    console.error('加载公告列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 搜索处理
 */
function handleSearch() {
  currentPage.value = 1;
  loadAnnouncementList();
}

/**
 * 清除搜索
 */
function handleClear() {
  searchKeyword.value = '';
  currentPage.value = 1;
  loadAnnouncementList();
}

/**
 * 加载更多
 */
function loadMore() {
  currentPage.value++;
  loadAnnouncementList(true);
}

/**
 * 跳转到详情页
 */
function goToDetail(item: InfoItem) {
  uni.navigateTo({
    url: `/subpackages/info/pages/detail/detail?id=${item.id}&type=announcement`,
  });
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天';
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.announcement-list-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $primary-color, $primary-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.search-section {
  padding: $spacing-md;
  background-color: white;
  border-bottom: 1rpx solid $divider-color;
}

.announcement-list {
  padding: $spacing-md;

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;

    .loading-text, .empty-text {
      margin-top: $spacing-md;
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .empty-icon {
      font-size: 120rpx;
      opacity: 0.5;
    }
  }

  .announcement-item {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-light;

    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: $spacing-sm;

      .item-title {
        flex: 1;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        line-height: 1.4;
        margin-right: $spacing-sm;
      }

      .top-badge {
        background-color: $error-color;
        color: white;
        font-size: $font-size-xs;
        padding: 2rpx 8rpx;
        border-radius: $border-radius-small;
        white-space: nowrap;
      }
    }

    .item-summary {
      display: block;
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.5;
      margin-bottom: $spacing-md;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .item-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-time, .item-views {
        font-size: $font-size-xs;
        color: $text-disabled;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: $spacing-lg;

  .load-more-text {
    font-size: $font-size-sm;
    color: $primary-color;
  }
}
</style>
