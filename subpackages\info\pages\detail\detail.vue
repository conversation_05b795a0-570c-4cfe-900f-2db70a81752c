<template>
  <view class="detail-container">
    <!-- 加载状态 -->
    <view
      v-if="loading"
      class="loading-state"
    >
      <u-loading-icon mode="spinner" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <!-- 详情内容 -->
    <view
      v-else-if="detailInfo"
      class="detail-content"
    >
      <!-- 文章头部 -->
      <view class="article-header">
        <text class="article-title">
          {{ detailInfo.title }}
        </text>
        
        <view class="article-meta">
          <view class="meta-item">
            <text class="meta-label">
              发布时间：
            </text>
            <text class="meta-value">
              {{ formatTime(detailInfo.publishTime) }}
            </text>
          </view>
          
          <view class="meta-item">
            <text class="meta-label">
              阅读量：
            </text>
            <text class="meta-value">
              {{ detailInfo.viewCount || 0 }}
            </text>
          </view>
          
          <view
            v-if="detailInfo.source"
            class="meta-item"
          >
            <text class="meta-label">
              来源：
            </text>
            <text class="meta-value">
              {{ detailInfo.source }}
            </text>
          </view>
        </view>
        
        <!-- 标签 -->
        <view
          v-if="detailInfo.tags && detailInfo.tags.length > 0"
          class="article-tags"
        >
          <view 
            v-for="tag in detailInfo.tags" 
            :key="tag"
            class="tag-item"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 文章内容 -->
      <view class="article-body">
        <!-- 摘要 -->
        <view
          v-if="detailInfo.summary"
          class="article-summary"
        >
          <text class="summary-title">
            摘要
          </text>
          <text class="summary-content">
            {{ detailInfo.summary }}
          </text>
        </view>
        
        <!-- 正文内容 -->
        <view class="article-content">
          <rich-text :nodes="detailInfo.content" />
        </view>
        
        <!-- 附件下载 -->
        <view
          v-if="detailInfo.attachments && detailInfo.attachments.length > 0"
          class="attachments"
        >
          <text class="attachments-title">
            相关附件
          </text>
          <view 
            v-for="attachment in detailInfo.attachments" 
            :key="attachment.id"
            class="attachment-item"
            @tap="downloadAttachment(attachment)"
          >
            <view class="attachment-icon">
              📎
            </view>
            <text class="attachment-name">
              {{ attachment.name }}
            </text>
            <text class="attachment-size">
              {{ formatFileSize(attachment.size) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button
          class="action-btn share-btn"
          @tap="shareArticle"
        >
          <u-icon
            name="share"
            size="16"
          />
          <text>分享</text>
        </button>
        
        <button
          class="action-btn collect-btn"
          :class="{ collected: isCollected }"
          @tap="toggleCollect"
        >
          <u-icon
            :name="isCollected ? 'heart-fill' : 'heart'"
            size="16"
          />
          <text>{{ isCollected ? '已收藏' : '收藏' }}</text>
        </button>
      </view>
    </view>

    <!-- 错误状态 -->
    <view
      v-else
      class="error-state"
    >
      <text class="error-icon">
        ❌
      </text>
      <text class="error-text">
        内容加载失败
      </text>
      <button
        class="retry-btn"
        @tap="loadDetail"
      >
        重新加载
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getInfoDetail } from '../../../../src/api/modules/info';
import type { InfoItem } from '../../../../src/types/api';

// 页面参数
const props = defineProps<{
  id: string;
  type: string;
}>();

// 响应式数据
const detailInfo = ref<InfoItem | null>(null);
const loading = ref(false);
const isCollected = ref(false);

onMounted(() => {
  loadDetail();
});

/**
 * 加载详情数据
 */
async function loadDetail() {
  if (!props.id) {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    });
    return;
  }
  
  try {
    loading.value = true;
    detailInfo.value = await getInfoDetail(props.id);
    
    // 检查收藏状态
    checkCollectStatus();
    
  } catch (error) {
    console.error('加载详情失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 检查收藏状态
 */
function checkCollectStatus() {
  // 从本地存储检查收藏状态
  const collectKey = `collect_${props.type}_${props.id}`;
  isCollected.value = uni.getStorageSync(collectKey) || false;
}

/**
 * 切换收藏状态
 */
function toggleCollect() {
  const collectKey = `collect_${props.type}_${props.id}`;
  isCollected.value = !isCollected.value;
  
  uni.setStorageSync(collectKey, isCollected.value);
  
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '取消收藏',
    icon: 'success',
  });
}

/**
 * 分享文章
 */
function shareArticle() {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: '',
    title: detailInfo.value?.title || '',
    summary: detailInfo.value?.summary || '',
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success',
      });
    },
    fail: (error) => {
      console.error('分享失败:', error);
      uni.showToast({
        title: '分享失败',
        icon: 'none',
      });
    },
  });
}

/**
 * 下载附件
 */
function downloadAttachment(attachment: any) {
  uni.showModal({
    title: '下载附件',
    content: `确定要下载 ${attachment.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        // 这里实现附件下载逻辑
        uni.showToast({
          title: '开始下载',
          icon: 'success',
        });
      }
    },
  });
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString();
}

/**
 * 格式化文件大小
 */
function formatFileSize(size: number) {
  if (size < 1024) {
    return `${size}B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)}KB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(1)}MB`;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.detail-container {
  min-height: 100vh;
  background-color: $background-color;
}

.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: $spacing-xl;

  .loading-text, .error-text {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    color: $text-secondary;
  }

  .error-icon {
    font-size: 120rpx;
    opacity: 0.5;
  }

  .retry-btn {
    margin-top: $spacing-lg;
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: $border-radius-medium;
    padding: $spacing-sm $spacing-lg;
    font-size: $font-size-md;
  }
}

.detail-content {
  background-color: white;

  .article-header {
    padding: $spacing-xl $spacing-lg $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .article-title {
      display: block;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      line-height: 1.4;
      margin-bottom: $spacing-lg;
    }

    .article-meta {
      margin-bottom: $spacing-md;

      .meta-item {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-xs;

        .meta-label {
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-right: $spacing-xs;
        }

        .meta-value {
          font-size: $font-size-sm;
          color: $text-primary;
        }
      }
    }

    .article-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;

      .tag-item {
        background-color: $primary-light;
        color: $primary-color;
        font-size: $font-size-xs;
        padding: 4rpx 8rpx;
        border-radius: $border-radius-small;
      }
    }
  }

  .article-body {
    padding: $spacing-lg;

    .article-summary {
      background-color: $background-color;
      border-radius: $border-radius-medium;
      padding: $spacing-md;
      margin-bottom: $spacing-lg;

      .summary-title {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-sm;
      }

      .summary-content {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.6;
      }
    }

    .article-content {
      font-size: $font-size-md;
      line-height: 1.8;
      color: $text-primary;
      margin-bottom: $spacing-xl;

      // rich-text 内容样式
      :deep(p) {
        margin-bottom: $spacing-md;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: $border-radius-small;
      }
    }

    .attachments {
      border-top: 1rpx solid $divider-color;
      padding-top: $spacing-lg;

      .attachments-title {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-md;
      }

      .attachment-item {
        display: flex;
        align-items: center;
        background-color: $background-color;
        border-radius: $border-radius-medium;
        padding: $spacing-md;
        margin-bottom: $spacing-sm;

        .attachment-icon {
          font-size: $font-size-lg;
          margin-right: $spacing-sm;
        }

        .attachment-name {
          flex: 1;
          font-size: $font-size-sm;
          color: $text-primary;
          margin-right: $spacing-sm;
        }

        .attachment-size {
          font-size: $font-size-xs;
          color: $text-disabled;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;

    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      height: 80rpx;
      border: 1rpx solid $divider-color;
      border-radius: $border-radius-medium;
      background-color: white;
      font-size: $font-size-sm;
      color: $text-secondary;

      &.share-btn:active {
        background-color: $primary-light;
        color: $primary-color;
      }

      &.collect-btn {
        &.collected {
          background-color: $error-light;
          color: $error-color;
          border-color: $error-color;
        }

        &:active {
          background-color: $error-light;
          color: $error-color;
        }
      }
    }
  }
}
</style>
