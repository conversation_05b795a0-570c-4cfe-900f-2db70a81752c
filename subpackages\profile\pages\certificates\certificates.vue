<template>
  <view class="certificates-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">
        证书管理
      </text>
      <text class="page-desc">
        查看和管理您的证书
      </text>
    </view>

    <!-- 证书统计 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">
            {{ totalCertificates }}
          </text>
          <text class="stats-label">
            总证书数
          </text>
        </view>
        
        <view class="stats-item">
          <text class="stats-number">
            {{ validCertificates }}
          </text>
          <text class="stats-label">
            有效证书
          </text>
        </view>
        
        <view class="stats-item">
          <text class="stats-number">
            {{ expiringSoon }}
          </text>
          <text class="stats-label">
            即将过期
          </text>
        </view>
      </view>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-section">
      <scroll-view
        class="filter-scroll"
        scroll-x
      >
        <view class="filter-tabs">
          <view 
            v-for="filter in filterOptions" 
            :key="filter.value"
            class="filter-tab"
            :class="{ active: selectedFilter === filter.value }"
            @tap="selectFilter(filter.value)"
          >
            {{ filter.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 证书列表 -->
    <view class="certificates-list">
      <view
        v-if="loading"
        class="loading-state"
      >
        <u-loading-icon mode="spinner" />
        <text class="loading-text">
          加载中...
        </text>
      </view>

      <view
        v-else-if="certificateList.length === 0"
        class="empty-state"
      >
        <text class="empty-icon">
          🏆
        </text>
        <text class="empty-text">
          暂无证书记录
        </text>
        <text class="empty-desc">
          完成考试并通过后可获得证书
        </text>
      </view>

      <view v-else>
        <view 
          v-for="certificate in certificateList" 
          :key="certificate.id"
          class="certificate-item"
          @tap="viewCertificate(certificate)"
        >
          <view class="certificate-header">
            <view
              class="certificate-icon"
              :class="certificate.status"
            >
              {{ getCertificateIcon(certificate.type) }}
            </view>
            
            <view class="certificate-info">
              <text class="certificate-name">
                {{ certificate.name }}
              </text>
              <text class="certificate-type">
                {{ getCertificateTypeText(certificate.type) }}
              </text>
            </view>
            
            <view
              class="certificate-status"
              :class="certificate.status"
            >
              {{ getStatusText(certificate.status) }}
            </view>
          </view>
          
          <view class="certificate-details">
            <view class="detail-row">
              <text class="detail-label">
                证书编号：
              </text>
              <text class="detail-value">
                {{ certificate.certificateNumber }}
              </text>
            </view>
            
            <view class="detail-row">
              <text class="detail-label">
                获得时间：
              </text>
              <text class="detail-value">
                {{ formatDate(certificate.issuedAt) }}
              </text>
            </view>
            
            <view class="detail-row">
              <text class="detail-label">
                有效期至：
              </text>
              <text
                class="detail-value"
                :class="{ expired: isExpired(certificate.expiresAt) }"
              >
                {{ certificate.expiresAt ? formatDate(certificate.expiresAt) : '长期有效' }}
              </text>
            </view>
            
            <view
              v-if="certificate.score"
              class="detail-row"
            >
              <text class="detail-label">
                考试成绩：
              </text>
              <text class="detail-value score">
                {{ certificate.score }}分
              </text>
            </view>
          </view>
          
          <view class="certificate-actions">
            <button
              class="action-btn view-btn"
              @tap.stop="viewCertificate(certificate)"
            >
              查看证书
            </button>
            
            <button 
              v-if="certificate.status === 'valid'"
              class="action-btn download-btn"
              @tap.stop="downloadCertificate(certificate)"
            >
              下载证书
            </button>
            
            <button 
              v-if="canRenew(certificate)"
              class="action-btn renew-btn"
              @tap.stop="renewCertificate(certificate)"
            >
              续期申请
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 证书详情弹窗 -->
    <u-modal
      v-model="showCertificateModal"
      title="证书详情"
      :show-cancel-button="false"
      confirm-text="关闭"
      @confirm="showCertificateModal = false"
    >
      <view
        v-if="selectedCertificate"
        class="certificate-modal"
      >
        <view class="modal-certificate">
          <view class="cert-header">
            <text class="cert-title">
              {{ selectedCertificate.name }}
            </text>
            <text class="cert-subtitle">
              {{ getCertificateTypeText(selectedCertificate.type) }}
            </text>
          </view>
          
          <view class="cert-body">
            <text class="cert-holder">
              持证人：{{ selectedCertificate.holderName }}
            </text>
            <text class="cert-number">
              证书编号：{{ selectedCertificate.certificateNumber }}
            </text>
            <text class="cert-date">
              颁发日期：{{ formatDate(selectedCertificate.issuedAt) }}
            </text>
            <text
              v-if="selectedCertificate.expiresAt"
              class="cert-expire"
            >
              有效期至：{{ formatDate(selectedCertificate.expiresAt) }}
            </text>
          </view>
          
          <view class="cert-footer">
            <text class="cert-issuer">
              {{ selectedCertificate.issuer || '疾控考试系统' }}
            </text>
          </view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getCertificateList, downloadCertificateFile, renewCertificate as renewCertificateApi } from '../../../../src/api/modules/certificate';

// 响应式数据
const certificateList = ref<any[]>([]);
const loading = ref(false);
const selectedFilter = ref('all');
const selectedCertificate = ref<any>(null);
const showCertificateModal = ref(false);

// 筛选选项
const filterOptions = ref([
  { label: '全部', value: 'all' },
  { label: '有效', value: 'valid' },
  { label: '即将过期', value: 'expiring' },
  { label: '已过期', value: 'expired' },
  { label: '已撤销', value: 'revoked' },
]);

// 计算属性
const totalCertificates = computed(() => certificateList.value.length);
const validCertificates = computed(() => 
  certificateList.value.filter(cert => cert.status === 'valid').length,
);
const expiringSoon = computed(() => 
  certificateList.value.filter(cert => {
    if (!cert.expiresAt) return false;
    const expiryDate = new Date(cert.expiresAt);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }).length,
);

onMounted(() => {
  loadCertificates();
});

/**
 * 加载证书列表
 */
async function loadCertificates() {
  try {
    loading.value = true;
    
    const params = {
      filter: selectedFilter.value === 'all' ? undefined : selectedFilter.value,
    };
    
    certificateList.value = await getCertificateList(params);
    
  } catch (error) {
    console.error('加载证书列表失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 选择筛选条件
 */
function selectFilter(filterValue: string) {
  selectedFilter.value = filterValue;
  loadCertificates();
}

/**
 * 获取证书图标
 */
function getCertificateIcon(type: string) {
  const iconMap: Record<string, string> = {
    qualification: '🎓',
    training: '📚',
    skill: '🛠️',
    achievement: '🏆',
  };
  return iconMap[type] || '📜';
}

/**
 * 获取证书类型文本
 */
function getCertificateTypeText(type: string) {
  const typeMap: Record<string, string> = {
    qualification: '资格证书',
    training: '培训证书',
    skill: '技能证书',
    achievement: '成就证书',
  };
  return typeMap[type] || type;
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    valid: '有效',
    expired: '已过期',
    expiring: '即将过期',
    revoked: '已撤销',
  };
  return statusMap[status] || status;
}

/**
 * 判断是否过期
 */
function isExpired(expiryDate: string) {
  if (!expiryDate) return false;
  return new Date(expiryDate) < new Date();
}

/**
 * 判断是否可以续期
 */
function canRenew(certificate: any) {
  return certificate.status === 'expiring' || certificate.status === 'expired';
}

/**
 * 查看证书
 */
function viewCertificate(certificate: any) {
  selectedCertificate.value = certificate;
  showCertificateModal.value = true;
}

/**
 * 下载证书
 */
async function downloadCertificate(certificate: any) {
  try {
    uni.showLoading({ title: '下载中...' });
    
    await downloadCertificateFile(certificate.id);
    
    uni.hideLoading();
    uni.showToast({
      title: '证书已保存到相册',
      icon: 'success',
    });
    
  } catch (error) {
    uni.hideLoading();
    console.error('下载证书失败:', error);
    uni.showToast({
      title: '下载失败，请重试',
      icon: 'none',
    });
  }
}

/**
 * 续期申请
 */
async function renewCertificate(certificate: any) {
  try {
    uni.showModal({
      title: '续期申请',
      content: `确定要申请续期"${certificate.name}"吗？`,
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({ title: '申请中...' });
          
          await renewCertificateApi(certificate.id);
          
          uni.hideLoading();
          uni.showToast({
            title: '续期申请已提交',
            icon: 'success',
          });
          
          // 刷新列表
          loadCertificates();
        }
      },
    });
  } catch (error) {
    uni.hideLoading();
    console.error('续期申请失败:', error);
    uni.showToast({
      title: '申请失败，请重试',
      icon: 'none',
    });
  }
}

/**
 * 格式化日期
 */
function formatDate(dateStr: string) {
  return new Date(dateStr).toLocaleDateString();
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.certificates-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $warning-color, $warning-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.stats-section {
  padding: $spacing-lg;

  .stats-card {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    display: flex;
    justify-content: space-around;
    box-shadow: $shadow-light;

    .stats-item {
      text-align: center;

      .stats-number {
        display: block;
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $warning-color;
        margin-bottom: $spacing-xs;
      }

      .stats-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.filter-section {
  background-color: white;
  border-bottom: 1rpx solid $divider-color;

  .filter-scroll {
    white-space: nowrap;

    .filter-tabs {
      display: flex;
      padding: $spacing-sm $spacing-md;

      .filter-tab {
        flex-shrink: 0;
        padding: $spacing-sm $spacing-md;
        margin-right: $spacing-sm;
        border-radius: $border-radius-medium;
        font-size: $font-size-sm;
        color: $text-secondary;
        background-color: $background-color;

        &.active {
          background-color: $warning-color;
          color: white;
        }
      }
    }
  }
}

.certificates-list {
  padding: 0 $spacing-lg $spacing-lg;

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;

    .loading-text, .empty-text {
      margin-top: $spacing-md;
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .empty-desc {
      margin-top: $spacing-sm;
      font-size: $font-size-sm;
      color: $text-disabled;
    }

    .empty-icon {
      font-size: 120rpx;
      opacity: 0.5;
    }
  }

  .certificate-item {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-light;

    .certificate-header {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-md;

      .certificate-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40rpx;
        margin-right: $spacing-md;

        &.valid {
          background-color: $success-light;
        }

        &.expired {
          background-color: $error-light;
        }

        &.expiring {
          background-color: $warning-light;
        }

        &.revoked {
          background-color: $text-disabled;
        }
      }

      .certificate-info {
        flex: 1;

        .certificate-name {
          display: block;
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-xs;
        }

        .certificate-type {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .certificate-status {
        font-size: $font-size-xs;
        padding: 4rpx 8rpx;
        border-radius: $border-radius-small;

        &.valid {
          background-color: $success-light;
          color: $success-color;
        }

        &.expired {
          background-color: $error-light;
          color: $error-color;
        }

        &.expiring {
          background-color: $warning-light;
          color: $warning-color;
        }

        &.revoked {
          background-color: $text-disabled;
          color: white;
        }
      }
    }

    .certificate-details {
      margin-bottom: $spacing-md;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-sm;

        .detail-label {
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-right: $spacing-sm;
          min-width: 160rpx;
        }

        .detail-value {
          flex: 1;
          font-size: $font-size-sm;
          color: $text-primary;

          &.expired {
            color: $error-color;
          }

          &.score {
            color: $success-color;
            font-weight: $font-weight-medium;
          }
        }
      }
    }

    .certificate-actions {
      display: flex;
      gap: $spacing-sm;

      .action-btn {
        flex: 1;
        height: 60rpx;
        border: none;
        border-radius: $border-radius-medium;
        font-size: $font-size-xs;

        &.view-btn {
          background-color: $info-light;
          color: $info-color;
        }

        &.download-btn {
          background-color: $success-light;
          color: $success-color;
        }

        &.renew-btn {
          background-color: $warning-light;
          color: $warning-color;
        }
      }
    }
  }
}

.certificate-modal {
  padding: $spacing-lg;

  .modal-certificate {
    background: linear-gradient(135deg, $warning-light, white);
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    text-align: center;
    border: 4rpx solid $warning-color;

    .cert-header {
      margin-bottom: $spacing-xl;

      .cert-title {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin-bottom: $spacing-sm;
      }

      .cert-subtitle {
        font-size: $font-size-md;
        color: $text-secondary;
      }
    }

    .cert-body {
      margin-bottom: $spacing-xl;

      text {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-sm;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .cert-footer {
      .cert-issuer {
        font-size: $font-size-sm;
        color: $text-disabled;
        font-style: italic;
      }
    }
  }
}
</style>
