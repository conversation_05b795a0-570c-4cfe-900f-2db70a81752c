<template>
  <view class="result-container">
    <!-- 结果头部 -->
    <view class="result-header">
      <view
        class="result-icon"
        :class="resultLevel"
      >
        {{ getResultIcon() }}
      </view>
      
      <text class="result-title">
        {{ getResultTitle() }}
      </text>
      <text class="result-subtitle">
        {{ categoryName }} 练习完成
      </text>
    </view>

    <!-- 成绩统计 -->
    <view class="score-section">
      <view class="score-card">
        <view class="score-main">
          <text class="score-number">
            {{ totalScore }}
          </text>
          <text class="score-unit">
            分
          </text>
        </view>
        
        <view class="score-details">
          <view class="detail-item">
            <text class="detail-label">
              正确率
            </text>
            <text class="detail-value">
              {{ correctRate }}%
            </text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">
              答对题数
            </text>
            <text class="detail-value">
              {{ correctCount }}/{{ totalCount }}
            </text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">
              用时
            </text>
            <text class="detail-value">
              {{ formatDuration(usedTime) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 成绩分析 -->
    <view class="analysis-section">
      <text class="section-title">
        成绩分析
      </text>
      
      <view class="analysis-card">
        <view class="analysis-item">
          <view class="analysis-icon excellent">
            🎯
          </view>
          <view class="analysis-content">
            <text class="analysis-title">
              答题准确性
            </text>
            <text class="analysis-desc">
              {{ getAccuracyAnalysis() }}
            </text>
          </view>
        </view>
        
        <view class="analysis-item">
          <view
            class="analysis-icon"
            :class="speedLevel"
          >
            ⚡
          </view>
          <view class="analysis-content">
            <text class="analysis-title">
              答题速度
            </text>
            <text class="analysis-desc">
              {{ getSpeedAnalysis() }}
            </text>
          </view>
        </view>
        
        <view class="analysis-item">
          <view
            class="analysis-icon"
            :class="knowledgeLevel"
          >
            📚
          </view>
          <view class="analysis-content">
            <text class="analysis-title">
              知识掌握
            </text>
            <text class="analysis-desc">
              {{ getKnowledgeAnalysis() }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 建议提升 -->
    <view class="suggestion-section">
      <text class="section-title">
        建议提升
      </text>
      
      <view class="suggestion-card">
        <view
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          class="suggestion-item"
        >
          <view class="suggestion-icon">
            {{ suggestion.icon }}
          </view>
          <text class="suggestion-text">
            {{ suggestion.text }}
          </text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button
        class="secondary-btn"
        @tap="reviewAnswers"
      >
        查看答案解析
      </button>
      
      <button
        class="primary-btn"
        @tap="continueStudy"
      >
        继续学习
      </button>
    </view>

    <!-- 分享成绩 -->
    <view class="share-section">
      <button
        class="share-btn"
        @tap="shareResult"
      >
        <u-icon
          name="share"
          size="16"
        />
        <text>分享成绩</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// 页面参数
const props = defineProps<{
  categoryName: string;
  totalScore: number;
  correctCount: number;
  totalCount: number;
}>();

// 响应式数据
const usedTime = ref(25 * 60); // 假设用时25分钟

// 计算属性
const correctRate = computed(() => 
  Math.round((props.correctCount / props.totalCount) * 100),
);

const resultLevel = computed(() => {
  if (correctRate.value >= 90) return 'excellent';
  if (correctRate.value >= 80) return 'good';
  if (correctRate.value >= 70) return 'average';
  return 'poor';
});

const speedLevel = computed(() => {
  const avgTimePerQuestion = usedTime.value / props.totalCount;
  if (avgTimePerQuestion <= 60) return 'excellent';
  if (avgTimePerQuestion <= 90) return 'good';
  return 'average';
});

const knowledgeLevel = computed(() => {
  if (correctRate.value >= 85) return 'excellent';
  if (correctRate.value >= 75) return 'good';
  return 'average';
});

const suggestions = computed(() => {
  const result = [];
  
  if (correctRate.value < 80) {
    result.push({
      id: 1,
      icon: '📖',
      text: '建议加强基础知识学习，多做相关练习题',
    });
  }
  
  if (speedLevel.value === 'average') {
    result.push({
      id: 2,
      icon: '⏰',
      text: '可以通过多练习来提高答题速度',
    });
  }
  
  if (correctRate.value >= 90) {
    result.push({
      id: 3,
      icon: '🎯',
      text: '表现优秀！可以尝试更高难度的题目',
    });
  } else {
    result.push({
      id: 4,
      icon: '💪',
      text: '继续努力，多练习可以提高正确率',
    });
  }
  
  return result;
});

onMounted(() => {
  // 记录练习结果到本地存储
  savePracticeResult();
});

/**
 * 获取结果图标
 */
function getResultIcon() {
  const iconMap: Record<string, string> = {
    excellent: '🏆',
    good: '👍',
    average: '📈',
    poor: '💪',
  };
  return iconMap[resultLevel.value] || '📊';
}

/**
 * 获取结果标题
 */
function getResultTitle() {
  const titleMap: Record<string, string> = {
    excellent: '优秀！',
    good: '良好！',
    average: '继续努力！',
    poor: '加油！',
  };
  return titleMap[resultLevel.value] || '练习完成';
}

/**
 * 获取准确性分析
 */
function getAccuracyAnalysis() {
  if (correctRate.value >= 90) return '答题准确性很高，基础扎实';
  if (correctRate.value >= 80) return '答题准确性良好，还有提升空间';
  if (correctRate.value >= 70) return '答题准确性一般，需要加强练习';
  return '答题准确性较低，建议重点复习';
}

/**
 * 获取速度分析
 */
function getSpeedAnalysis() {
  const avgTime = Math.round(usedTime.value / props.totalCount);
  if (avgTime <= 60) return `平均${avgTime}秒/题，答题速度很快`;
  if (avgTime <= 90) return `平均${avgTime}秒/题，答题速度适中`;
  return `平均${avgTime}秒/题，可以提高答题速度`;
}

/**
 * 获取知识掌握分析
 */
function getKnowledgeAnalysis() {
  if (correctRate.value >= 85) return '知识掌握程度很好';
  if (correctRate.value >= 75) return '知识掌握程度良好';
  return '知识掌握程度有待提高';
}

/**
 * 格式化时长
 */
function formatDuration(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}分${remainingSeconds}秒`;
}

/**
 * 保存练习结果
 */
function savePracticeResult() {
  const result = {
    categoryName: props.categoryName,
    totalScore: props.totalScore,
    correctCount: props.correctCount,
    totalCount: props.totalCount,
    correctRate: correctRate.value,
    usedTime: usedTime.value,
    timestamp: Date.now(),
  };
  
  // 保存到本地存储
  const historyKey = 'practice_history';
  const history = uni.getStorageSync(historyKey) || [];
  history.unshift(result);
  
  // 只保留最近20条记录
  if (history.length > 20) {
    history.splice(20);
  }
  
  uni.setStorageSync(historyKey, history);
}

/**
 * 查看答案解析
 */
function reviewAnswers() {
  // 这里可以跳转到答案解析页面
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  });
}

/**
 * 继续学习
 */
function continueStudy() {
  uni.navigateBack({
    delta: 2, // 返回到题库分类页面
  });
}

/**
 * 分享成绩
 */
function shareResult() {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    title: `我在疾控考试系统完成了${props.categoryName}练习`,
    summary: `获得${props.totalScore}分，正确率${correctRate.value}%，快来一起学习吧！`,
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success',
      });
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
        icon: 'none',
      });
    },
  });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-light, $background-color);
  padding: $spacing-lg;
}

.result-header {
  text-align: center;
  padding: $spacing-xxl $spacing-lg;

  .result-icon {
    display: block;
    font-size: 200rpx;
    margin-bottom: $spacing-lg;

    &.excellent {
      filter: hue-rotate(45deg) brightness(1.2);
    }

    &.good {
      filter: hue-rotate(120deg);
    }

    &.average {
      filter: hue-rotate(200deg);
    }

    &.poor {
      filter: hue-rotate(300deg);
    }
  }

  .result-title {
    display: block;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  .result-subtitle {
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.score-section {
  margin-bottom: $spacing-xl;

  .score-card {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    box-shadow: $shadow-medium;

    .score-main {
      text-align: center;
      margin-bottom: $spacing-xl;

      .score-number {
        font-size: 120rpx;
        font-weight: $font-weight-bold;
        color: $primary-color;
        line-height: 1;
      }

      .score-unit {
        font-size: $font-size-lg;
        color: $text-secondary;
        margin-left: $spacing-xs;
      }
    }

    .score-details {
      display: flex;
      justify-content: space-around;

      .detail-item {
        text-align: center;

        .detail-label {
          display: block;
          font-size: $font-size-sm;
          color: $text-secondary;
          margin-bottom: $spacing-xs;
        }

        .detail-value {
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $text-primary;
        }
      }
    }
  }
}

.analysis-section, .suggestion-section {
  margin-bottom: $spacing-xl;

  .section-title {
    display: block;
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-md;
  }

  .analysis-card, .suggestion-card {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    box-shadow: $shadow-light;
  }
}

.analysis-section {
  .analysis-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }

    .analysis-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      margin-right: $spacing-md;
      flex-shrink: 0;

      &.excellent {
        background-color: $success-light;
      }

      &.good {
        background-color: $primary-light;
      }

      &.average {
        background-color: $warning-light;
      }
    }

    .analysis-content {
      flex: 1;

      .analysis-title {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .analysis-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.5;
      }
    }
  }
}

.suggestion-section {
  .suggestion-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }

    .suggestion-icon {
      font-size: $font-size-lg;
      margin-right: $spacing-sm;
      margin-top: 2rpx;
    }

    .suggestion-text {
      flex: 1;
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.5;
    }
  }
}

.action-section {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;

  .secondary-btn, .primary-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
  }

  .secondary-btn {
    background-color: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;

    &:active {
      background-color: $primary-light;
    }
  }

  .primary-btn {
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    color: white;

    &:active {
      opacity: 0.8;
    }
  }
}

.share-section {
  text-align: center;

  .share-btn {
    display: inline-flex;
    align-items: center;
    gap: $spacing-xs;
    background-color: transparent;
    color: $text-secondary;
    border: none;
    font-size: $font-size-sm;
    padding: $spacing-sm $spacing-md;

    &:active {
      color: $primary-color;
    }
  }
}
</style>
