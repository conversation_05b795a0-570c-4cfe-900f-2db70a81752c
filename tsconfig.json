{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/api/*": ["src/api/*"], "@/stores/*": ["src/stores/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/styles/*": ["src/styles/*"]}, "types": ["@dcloudio/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "pages/**/*.vue", "components/**/*.vue", "*.ts", "*.js"], "exclude": ["node_modules", "unpackage", "dist"], "vueCompilerOptions": {"target": 3}}