<template>
  <uvImage 
    :src="src"
    :mode="mode"
    :width="width"
    :height="height"
    :shape="shape"
    :radius="radius"
    :lazy-load="lazyLoad"
    :show-menu-by-longpress="showMenuByLongpress"
    :loading-icon="loadingIcon"
    :error-icon="errorIcon"
    :show-loading="showLoading"
    :show-error="showError"
    :fade="fade"
    :webp="webp"
    :duration="duration"
    :bg-color="bgColor"
    :custom-style="customStyle"
    @click="$emit('click')"
    @error="$emit('error')"
    @load="$emit('load')"
  >
    <template #loading>
      <slot name="loading" />
    </template>
    <template #error>
      <slot name="error" />
    </template>
  </uvImage>
</template>

<script>
/**
	 * 此组件存在的理由是，在nvue下，u-image被uni-app官方占用了，u-image在nvue中相当于image组件
	 * 所以在nvue下，取名为u--image，内部其实还是u-iamge.vue，只不过做一层中转
	 */
import uvImage from '../u-image/u-image.vue';
import { props } from '../u-image/props.js';
import { mpMixin } from '../../libs/mixin/mpMixin';
import { mixin } from '../../libs/mixin/mixin';
export default {
  name: 'u--image',
  components: {
    uvImage,
  },
  mixins: [mpMixin, props, mixin],
  emits: ['click', 'error', 'load'],
};
</script>