<template>
  <uvText
    :type="type"
    :show="show"
    :text="text"
    :prefix-icon="prefixIcon"
    :suffix-icon="suffixIcon"
    :mode="mode"
    :href="href"
    :format="format"
    :call="call"
    :open-type="openType"
    :bold="bold"
    :block="block"
    :lines="lines"
    :color="color"
    :decoration="decoration"
    :size="size"
    :icon-style="iconStyle"
    :margin="margin"
    :line-height="lineHeight"
    :align="align"
    :word-wrap="wordWrap"
    :custom-style="customStyle"
  />
</template>

<script>
/**
 * 此组件存在的理由是，在nvue下，u-text被uni-app官方占用了，u-text在nvue中相当于input组件
 * 所以在nvue下，取名为u--input，内部其实还是u-text.vue，只不过做一层中转
 * 不使用v-bind="$attrs"，而是分开独立写传参，是因为微信小程序不支持此写法
 */
import uvText from '../u-text/u-text.vue';
import { props } from '../u-text/props.js';
import { mpMixin } from '../../libs/mixin/mpMixin.js';
import { mixin } from '../../libs/mixin/mixin.js';
export default {
  name: 'u--text',
  components: {
    uvText,
  },
  mixins: [mpMixin, mixin, props],
};
</script>
