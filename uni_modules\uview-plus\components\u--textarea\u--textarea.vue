<template>
  <uvTextarea
    :value="value"
    :model-value="modelValue"
    :placeholder="placeholder"
    :height="height"
    :confirm-type="confirmType"
    :disabled="disabled"
    :count="count"
    :focus="focus"
    :auto-height="autoHeight"
    :fixed="fixed"
    :cursor-spacing="cursorSpacing"
    :cursor="cursor"
    :show-confirm-bar="showConfirmBar"
    :selection-start="selectionStart"
    :selection-end="selectionEnd"
    :adjust-position="adjustPosition"
    :disable-default-padding="disableDefaultPadding"
    :hold-keyboard="holdKeyboard"
    :maxlength="maxlength"
    :border="border"
    :custom-style="customStyle"
    :formatter="formatter"
    :ignore-composition-event="ignoreCompositionEvent"
    @input="e => $emit('input', e)"
    @update:model-value="e => $emit('update:modelValue', e)"
  />
</template>

<script>
/**
	 * 此组件存在的理由是，在nvue下，u--textarea被uni-app官方占用了，u-textarea在nvue中相当于textarea组件
	 * 所以在nvue下，取名为u--textarea，内部其实还是u-textarea.vue，只不过做一层中转
	 */
import uvTextarea from '../u-textarea/u-textarea.vue';
import { props } from '../u-textarea/props.js';
import { mpMixin } from '../../libs/mixin/mpMixin';
import { mixin } from '../../libs/mixin/mixin';
export default {
  name: 'u--textarea',
  components: {
    uvTextarea,
  },
  mixins: [mpMixin, props, mixin],
};
</script>
