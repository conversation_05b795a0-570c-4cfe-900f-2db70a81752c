<template>
  <view class="u-picker-data">
    <view class="u-picker-data__trigger">
      <slot
        name="trigger"
        :current="current"
      />
      <up-input
        v-if="!$slots['trigger']"
        :model-value="current"
        disabled
        disabled-color="#ffffff"
        :placeholder="title"
        border="none"
      />
      <view
        class="u-picker-data__trigger__cover"
        @click="show = true"
      />
    </view>
    <up-picker
      :show="show"
      :columns="optionsInner"
      :key-name="labelKey"
      :default-index="defaultIndex"
      @confirm="select"
      @cancel="cancel"
    />
  </view>
</template>

<script>
export default {
  props: {
    modelValue: {
      type: [String, Number],
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    options: {
      type: Array,
      default: () => {
        return [];
      },
    },
    valueKey: {
      type: String,
      default: 'id',
    },
    labelKey: {
      type: String,
      default: 'name',
    },
  },
  emits: ['update:modelValue'],
  data() {
    return {
      show: false,
      current: '',
      defaultIndex: [],
    };
  },
  computed: {
    optionsInner() {
      return [this.options];
    },
  },
  watch: {
    modelValue() {
      if (this.modelValue) {
        this.options.forEach((ele, index) => {
          if (ele[this.valueKey] == this.modelValue) {
            this.current = ele[this.labelKey];
            this.defaultIndex = [index];
          }
        });
      }
    },
  },
  created() {
    if (this.modelValue) {
      this.options.forEach((ele, index) => {
        if (ele[this.valueKey] == this.modelValue) {
          this.current = ele[this.labelKey];
          this.defaultIndex = [index];
        }
      });
    }
  },
  methods: {
    hideKeyboard() {
      uni.hideKeyboard();
    },
    cancel() {
      this.show = false;
    },
    select(e) {
      const {
			    columnIndex,
			    index,
        value,
      } = e;
      this.show = false;
      // console.log(value);
      this.$emit('update:modelValue', value[0][this.valueKey]);
      this.defaultIndex = columnIndex;
      this.current = value[0][this.labelKey];
    },
  },
};
</script>

<style lang="scss" scoped>
	.u-picker-data {
		&__trigger {
			position: relative;
			&__cover {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
			}
		}
	}
</style>