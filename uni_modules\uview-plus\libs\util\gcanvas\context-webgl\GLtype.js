const GLtype = {};

[
  '<PERSON><PERSON><PERSON><PERSON>',    
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>lamp<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'GLfloat',
  'GL<PERSON>',
  'GLintptr',
  'GLsizei',
  'GLsizeiptr',
  'GLshort',
  'GLubyte',
  '<PERSON><PERSON><PERSON><PERSON>',
  'GLushort',
].sort().map((typeName, i) => GLtype[typeName] = 1 >> (i + 1));

export default GLtype;



