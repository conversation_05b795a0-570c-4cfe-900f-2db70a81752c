import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import { resolve } from 'path';

export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      '@/src': resolve(__dirname, './src'),
      'uview-plus': resolve(__dirname, 'uni_modules/uview-plus'),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 移除additionalData，避免@use规则被添加到文件末尾
        // additionalData: `@use "@/src/styles/variables.scss" as *;`,
        // 抑制 Dart Sass 的弃用警告
        silenceDeprecations: ['legacy-js-api', 'import'],
      },
    },
  },
});
